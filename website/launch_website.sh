#!/bin/bash

# TimeSync Website Launcher
# This script opens the TimeSync website in the default browser

echo "🚀 Launching TimeSync Website..."
echo "📱 TimeSync - Smart Attendance Management System"
echo ""

# Get the current directory
WEBSITE_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
WEBSITE_FILE="$WEBSITE_DIR/index.html"

# Check if the website file exists
if [ -f "$WEBSITE_FILE" ]; then
    echo "✅ Website file found: $WEBSITE_FILE"
    echo "🌐 Opening in default browser..."
    
    # Open in default browser (works on macOS, Linux, and Windows)
    if command -v open >/dev/null 2>&1; then
        # macOS
        open "$WEBSITE_FILE"
    elif command -v xdg-open >/dev/null 2>&1; then
        # Linux
        xdg-open "$WEBSITE_FILE"
    elif command -v start >/dev/null 2>&1; then
        # Windows
        start "$WEBSITE_FILE"
    else
        echo "❌ Could not detect system browser command"
        echo "📂 Please manually open: $WEBSITE_FILE"
    fi
    
    echo ""
    echo "🎉 TimeSync website should now be open in your browser!"
    echo ""
    echo "📋 Website Features:"
    echo "   • App overview and features"
    echo "   • Direct APK download (50MB)"
    echo "   • User testimonials"
    echo "   • Contact information"
    echo ""
    echo "💾 APK Location: $WEBSITE_DIR/TimeSync_v1.0_Release.apk"
    
else
    echo "❌ Error: Website file not found!"
    echo "📂 Expected location: $WEBSITE_FILE"
    echo "🔧 Please ensure you're running this script from the website directory"
fi

echo ""
echo "✨ Thank you for using TimeSync!"
