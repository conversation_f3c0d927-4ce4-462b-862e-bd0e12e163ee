# TimeSync Website

This website showcases the TimeSync attendance management app built with Flutter and Firebase.

## Features Highlighted

### 🏠 **Homepage Sections:**
- **Hero Section** - Main introduction to TimeSync
- **Key Features** - GPS tracking, multi-company support, analytics
- **About** - Information about the app and its capabilities
- **Statistics** - Key metrics and performance indicators
- **App Features** - Detailed feature breakdown
- **Download** - APK download and system requirements
- **User Reviews** - Testimonials from companies using TimeSync
- **Contact** - Support and contact information

### 📱 **App Information:**
- **Real-time GPS Tracking** - Automatic attendance detection
- **Multi-Company Support** - Manage multiple organizations
- **Advanced Analytics** - Comprehensive reporting and insights
- **Role-Based Security** - Three-tier access control
- **Cloud Synchronization** - Firebase integration with offline support
- **Employee Management** - Complete workforce management solution

### 💾 **Download Options:**
- **Android APK** - Direct download (50MB)
- **iOS App** - Coming soon
- **System Requirements** - Compatibility information

## Template Information

This website is built using the eNno Bootstrap template from BootstrapMade, customized specifically for TimeSync.

### Original Template:
- **Name:** eNno
- **Source:** BootstrapMade
- **License:** https://bootstrapmade.com/license/

### Customizations Made:
- Updated all content for TimeSync app
- Modified navigation menu
- Replaced portfolio section with download section
- Updated testimonials with app-specific reviews
- Changed branding and colors to match TimeSync
- Added APK download functionality

## File Structure

```
website/
├── index.html              # Main website file
├── TimeSync_v1.0_Release.apk # Android APK download
├── assets/                 # CSS, JS, images, and vendor files
├── forms/                  # Contact and newsletter forms
└── README_TimeSync.md      # This file
```

## Usage

1. Open `index.html` in a web browser
2. Navigate through the sections to learn about TimeSync
3. Download the APK from the Download section
4. Use the contact form for support inquiries

## Features Demonstrated

The website effectively showcases:
- ✅ Professional presentation of the TimeSync app
- ✅ Clear feature explanations with icons
- ✅ User testimonials and reviews
- ✅ Direct APK download capability
- ✅ Responsive design for all devices
- ✅ Modern UI with smooth animations
- ✅ Contact and newsletter signup forms

## Technical Details

- **Framework:** Bootstrap 5.3.3
- **Icons:** Bootstrap Icons
- **Animations:** AOS (Animate On Scroll)
- **Carousel:** Swiper.js for testimonials
- **Responsive:** Mobile-first design
- **Performance:** Optimized assets and loading

Perfect for showcasing the TimeSync attendance management app to potential users and companies!
