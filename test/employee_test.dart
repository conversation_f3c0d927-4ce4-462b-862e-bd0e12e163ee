import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:timesync/services/database_service.dart';
import 'package:timesync/models/employee.dart';

import 'package:timesync/models/attendance.dart';
import 'package:timesync/screens/employee/employee_onboarding_screen.dart';
import 'package:timesync/screens/employee/employee_dashboard_screen.dart';

void main() {
  group('Employee Unit Tests', () {
    late DatabaseService databaseService;

    setUpAll(() async {
      databaseService = DatabaseService();
    });

    test('Employee Model - Creation and Serialization', () {
      final employee = Employee(
        id: 'test-employee-id',
        name: '<PERSON>',
        phoneNumber: '+1234567890',
        designation: 'Software Engineer',
        email: '<EMAIL>',
        companyId: 'test-company-id',
        companyName: 'Test Company',
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
      );

      expect(employee.id, 'test-employee-id');
      expect(employee.name, '<PERSON>');
      expect(employee.email, '<EMAIL>');
      expect(employee.designation, 'Software Engineer');
      expect(employee.companyId, 'test-company-id');
      expect(employee.companyName, 'Test Company');
      expect(employee.isActive, true);

      // Test serialization
      final map = employee.toMap();
      expect(map['name'], 'John Doe');
      expect(map['email'], '<EMAIL>');
      expect(map['designation'], 'Software Engineer');
      expect(map['companyId'], 'test-company-id');
      expect(map['companyName'], 'Test Company');
      expect(map['isActive'], true);

      // Test deserialization
      final recreatedEmployee = Employee.fromMap(map, 'test-employee-id');
      expect(recreatedEmployee.id, employee.id);
      expect(recreatedEmployee.name, employee.name);
      expect(recreatedEmployee.email, employee.email);
      expect(recreatedEmployee.designation, employee.designation);
      expect(recreatedEmployee.companyId, employee.companyId);
      expect(recreatedEmployee.companyName, employee.companyName);
    });

    test('Attendance Model - Creation and Work Duration', () {
      final checkInTime = DateTime.now();
      final checkOutTime = checkInTime.add(const Duration(hours: 8, minutes: 30));

      final attendance = Attendance(
        id: 'test-attendance-id',
        employeeId: 'test-employee-id',
        employeeName: 'John Doe',
        companyId: 'test-company-id',
        date: DateTime.now(),
        checkInTime: checkInTime,
        checkInLatitude: 37.4220,
        checkInLongitude: -122.0841,
        checkOutTime: checkOutTime,
        checkOutLatitude: 37.4221,
        checkOutLongitude: -122.0842,
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
      );

      expect(attendance.id, 'test-attendance-id');
      expect(attendance.employeeId, 'test-employee-id');
      expect(attendance.employeeName, 'John Doe');
      expect(attendance.companyId, 'test-company-id');
      expect(attendance.isCheckedIn, false);

      // Test work duration calculation
      final workDuration = attendance.workDuration;
      expect(workDuration, isNotNull);
      expect(workDuration!.inHours, 8);
      expect(workDuration.inMinutes, 510); // 8 hours 30 minutes

      // Test work duration string
      expect(attendance.workDurationString, '8h 30m');

      // Test serialization
      final map = attendance.toMap();
      expect(map['employeeId'], 'test-employee-id');
      expect(map['employeeName'], 'John Doe');
      expect(map['companyId'], 'test-company-id');
      expect(map['checkInLatitude'], 37.4220);
      expect(map['checkInLongitude'], -122.0841);
      expect(map['checkOutLatitude'], 37.4221);
      expect(map['checkOutLongitude'], -122.0842);
    });

    test('Attendance Model - Still Working (No Check-out)', () {
      final attendance = Attendance(
        id: 'test-attendance-id',
        employeeId: 'test-employee-id',
        employeeName: 'John Doe',
        companyId: 'test-company-id',
        date: DateTime.now(),
        checkInTime: DateTime.now(),
        checkInLatitude: 37.4220,
        checkInLongitude: -122.0841,
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
      );

      expect(attendance.isCheckedIn, true);
      expect(attendance.workDuration, isNull);
      expect(attendance.workDurationString, 'Still working...');
    });

    testWidgets('Employee Onboarding Screen Widget Test', (WidgetTester tester) async {
      await tester.pumpWidget(
        const MaterialApp(
          home: EmployeeOnboardingScreen(),
        ),
      );

      // Verify initial elements are present
      expect(find.text('Join Your Team'), findsOneWidget);
      expect(find.text('Create your employee account'), findsOneWidget);
      expect(find.text('Personal Information'), findsOneWidget);
      expect(find.text('Full Name'), findsOneWidget);
      expect(find.text('Designation'), findsOneWidget);
      expect(find.text('Next'), findsOneWidget);

      // Test form validation
      await tester.tap(find.text('Next'));
      await tester.pumpAndSettle();

      // Should show error for empty fields
      expect(find.textContaining('Please fill all required fields'), findsOneWidget);
    });

    testWidgets('Employee Onboarding - Step Navigation', (WidgetTester tester) async {
      await tester.pumpWidget(
        const MaterialApp(
          home: EmployeeOnboardingScreen(),
        ),
      );

      // Fill personal information
      await tester.enterText(find.byType(TextFormField).first, 'John Doe');
      await tester.enterText(find.byType(TextFormField).last, 'Software Engineer');

      // Go to next step
      await tester.tap(find.text('Next'));
      await tester.pumpAndSettle();

      // Should be on company selection step
      expect(find.text('Select Your Company'), findsOneWidget);
      expect(find.text('Previous'), findsOneWidget);

      // Go back to previous step
      await tester.tap(find.text('Previous'));
      await tester.pumpAndSettle();

      // Should be back on personal information step
      expect(find.text('Personal Information'), findsOneWidget);
      expect(find.text('John Doe'), findsOneWidget);
      expect(find.text('Software Engineer'), findsOneWidget);
    });

    testWidgets('Employee Dashboard Widget Test', (WidgetTester tester) async {
      await tester.pumpWidget(
        const MaterialApp(
          home: EmployeeDashboardScreen(),
        ),
      );

      // Should show loading initially (since no user is logged in)
      expect(find.byType(CircularProgressIndicator), findsOneWidget);
    });

    test('Employee Model - Copy With Method', () {
      final originalEmployee = Employee(
        id: 'test-id',
        name: 'John Doe',
        phoneNumber: '+1234567890',
        designation: 'Developer',
        email: '<EMAIL>',
        companyId: 'company-1',
        companyName: 'Test Company',
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
      );

      final updatedEmployee = originalEmployee.copyWith(
        name: 'Jane Doe',
        designation: 'Senior Developer',
      );

      expect(updatedEmployee.id, originalEmployee.id);
      expect(updatedEmployee.name, 'Jane Doe');
      expect(updatedEmployee.email, originalEmployee.email);
      expect(updatedEmployee.designation, 'Senior Developer');
      expect(updatedEmployee.companyId, originalEmployee.companyId);
      expect(updatedEmployee.companyName, originalEmployee.companyName);
    });

    test('Attendance Model - Copy With Method', () {
      final originalAttendance = Attendance(
        id: 'test-id',
        employeeId: 'emp-1',
        employeeName: 'John Doe',
        companyId: 'company-1',
        date: DateTime.now(),
        checkInTime: DateTime.now(),
        checkInLatitude: 37.4220,
        checkInLongitude: -122.0841,
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
      );

      final checkOutTime = DateTime.now().add(const Duration(hours: 8));
      final updatedAttendance = originalAttendance.copyWith(
        checkOutTime: checkOutTime,
        checkOutLatitude: 37.4221,
        checkOutLongitude: -122.0842,
      );

      expect(updatedAttendance.id, originalAttendance.id);
      expect(updatedAttendance.employeeId, originalAttendance.employeeId);
      expect(updatedAttendance.checkInTime, originalAttendance.checkInTime);
      expect(updatedAttendance.checkOutTime, checkOutTime);
      expect(updatedAttendance.checkOutLatitude, 37.4221);
      expect(updatedAttendance.checkOutLongitude, -122.0842);
      expect(updatedAttendance.isCheckedIn, false);
    });

    test('Employee Email Validation', () {
      // Valid emails
      expect(RegExp(r'^[\w-\.]+@([\w-]+\.)+[\w-]{2,4}$').hasMatch('<EMAIL>'), true);
      expect(RegExp(r'^[\w-\.]+@([\w-]+\.)+[\w-]{2,4}$').hasMatch('<EMAIL>'), true);
      expect(RegExp(r'^[\w-\.]+@([\w-]+\.)+[\w-]{2,4}$').hasMatch('<EMAIL>'), true);

      // Invalid emails
      expect(RegExp(r'^[\w-\.]+@([\w-]+\.)+[\w-]{2,4}$').hasMatch('invalid-email'), false);
      expect(RegExp(r'^[\w-\.]+@([\w-]+\.)+[\w-]{2,4}$').hasMatch('test@'), false);
      expect(RegExp(r'^[\w-\.]+@([\w-]+\.)+[\w-]{2,4}$').hasMatch('@example.com'), false);
      expect(RegExp(r'^[\w-\.]+@([\w-]+\.)+[\w-]{2,4}$').hasMatch('test@.com'), false);
    });

    test('Work Duration Calculations', () {
      // Test various work durations
      final testCases = [
        {'hours': 8, 'minutes': 0, 'expected': '8h 0m'},
        {'hours': 8, 'minutes': 30, 'expected': '8h 30m'},
        {'hours': 4, 'minutes': 15, 'expected': '4h 15m'},
        {'hours': 12, 'minutes': 45, 'expected': '12h 45m'},
        {'hours': 0, 'minutes': 30, 'expected': '0h 30m'},
      ];

      for (final testCase in testCases) {
        final checkInTime = DateTime.now();
        final checkOutTime = checkInTime.add(Duration(
          hours: testCase['hours'] as int,
          minutes: testCase['minutes'] as int,
        ));

        final attendance = Attendance(
          id: 'test-id',
          employeeId: 'emp-1',
          employeeName: 'Test Employee',
          companyId: 'company-1',
          date: DateTime.now(),
          checkInTime: checkInTime,
          checkInLatitude: 0.0,
          checkInLongitude: 0.0,
          checkOutTime: checkOutTime,
          checkOutLatitude: 0.0,
          checkOutLongitude: 0.0,
          createdAt: DateTime.now(),
          updatedAt: DateTime.now(),
        );

        expect(attendance.workDurationString, testCase['expected']);
      }
    });

    test('Employee Model Equality', () {
      final employee1 = Employee(
        id: 'test-id',
        name: 'John Doe',
        phoneNumber: '+1234567890',
        designation: 'Developer',
        email: '<EMAIL>',
        companyId: 'company-1',
        companyName: 'Test Company',
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
      );

      final employee2 = Employee(
        id: 'test-id',
        name: 'Jane Doe', // Different name
        phoneNumber: '+0987654321', // Different phone
        designation: 'Designer', // Different designation
        email: '<EMAIL>', // Different email
        companyId: 'company-1',
        companyName: 'Test Company',
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
      );

      final employee3 = Employee(
        id: 'different-id',
        name: 'John Doe',
        phoneNumber: '+1234567890',
        designation: 'Developer',
        email: '<EMAIL>',
        companyId: 'company-1',
        companyName: 'Test Company',
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
      );

      // Same ID should be equal
      expect(employee1, equals(employee2));
      expect(employee1.hashCode, equals(employee2.hashCode));

      // Different ID should not be equal
      expect(employee1, isNot(equals(employee3)));
      expect(employee1.hashCode, isNot(equals(employee3.hashCode)));
    });
  });
}
