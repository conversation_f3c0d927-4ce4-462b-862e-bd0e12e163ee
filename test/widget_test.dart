// This is a basic Flutter widget test.
//
// To perform an interaction with a widget in your test, use the WidgetTester
// utility in the flutter_test package. For example, you can send tap and scroll
// gestures. You can also use WidgetTester to find child widgets in the widget
// tree, read text, and verify that the values of widget properties are correct.

import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';

import 'package:timesync/main.dart';

void main() {
  testWidgets('App shows splash screen then black page', (WidgetTester tester) async {
    // Build our app and trigger a frame.
    await tester.pumpWidget(const TimeSyncApp());

    // Verify that splash screen shows timeSync text
    expect(find.text('timeSync'), findsOneWidget);
    expect(find.text('Synchronize Your Time'), findsOneWidget);

    // Wait for splash screen to complete (3 seconds + some buffer)
    await tester.pumpAndSettle(const Duration(seconds: 4));

    // Verify that we're now on the black page (no text should be visible)
    expect(find.text('timeSync'), findsNothing);
    expect(find.text('Synchronize Your Time'), findsNothing);

    // Verify the black page is displayed by checking for the ColoredBox
    expect(find.byType(ColoredBox), findsOneWidget);
  });

  testWidgets('Splash screen shows timeSync branding', (WidgetTester tester) async {
    // Build our app and trigger a frame.
    await tester.pumpWidget(const TimeSyncApp());

    // Verify that splash screen shows timeSync text
    expect(find.text('timeSync'), findsOneWidget);
    expect(find.text('Synchronize Your Time'), findsOneWidget);

    // Verify loading indicator is present
    expect(find.byType(CircularProgressIndicator), findsOneWidget);

    // Verify clock icon is present
    expect(find.byIcon(Icons.access_time_rounded), findsOneWidget);
  });
}
