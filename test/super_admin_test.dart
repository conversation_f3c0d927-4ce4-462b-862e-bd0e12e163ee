import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:integration_test/integration_test.dart';
import 'package:timesync/main.dart' as app;
import 'package:timesync/services/super_admin_service.dart';
import 'package:timesync/services/sample_data_service.dart';
import 'package:timesync/services/database_service.dart';

void main() {
  IntegrationTestWidgetsFlutterBinding.ensureInitialized();

  group('Super Admin Tests', () {
    late SuperAdminService superAdminService;
    late SampleDataService sampleDataService;
    late DatabaseService databaseService;

    setUpAll(() async {
      superAdminService = SuperAdminService();
      sampleDataService = SampleDataService();
      databaseService = DatabaseService();
    });

    testWidgets('Test Super Admin Access - Case Sensitive', (WidgetTester tester) async {
      // Start the app
      app.main();
      await tester.pumpAndSettle();

      // Test 1: Access Super Admin with correct case
      await _testSuperAdminAccess(tester, 'ADMIN2024', shouldSucceed: true);

      // Test 2: Access Super Admin with wrong case (should fail)
      await _testSuperAdminAccess(tester, 'admin2024', shouldSucceed: false);
      await _testSuperAdminAccess(tester, 'Admin2024', shouldSucceed: false);
    });

    testWidgets('Test Super Admin Dashboard Features', (WidgetTester tester) async {
      // Start the app and login as Super Admin
      app.main();
      await tester.pumpAndSettle();
      await _loginAsSuperAdmin(tester);

      // Test Dashboard Elements
      expect(find.text('Super Admin Dashboard'), findsOneWidget);
      expect(find.text('Total Companies'), findsOneWidget);
      expect(find.text('Active Today'), findsOneWidget);
      expect(find.text('Super Admin Access Code'), findsOneWidget);
      expect(find.text('Create Company'), findsOneWidget);
      expect(find.text('Sample Data'), findsOneWidget);

      // Test Access Code Display
      expect(find.text('ADMIN2024'), findsOneWidget);
    });

    testWidgets('Test Sample Companies Creation', (WidgetTester tester) async {
      // Start the app and login as Super Admin
      app.main();
      await tester.pumpAndSettle();
      await _loginAsSuperAdmin(tester);

      // Find and tap Sample Data button
      await tester.tap(find.text('Sample Data'));
      await tester.pumpAndSettle();

      // Wait for loading dialog to appear and disappear
      await tester.pump(const Duration(seconds: 2));
      await tester.pumpAndSettle();

      // Verify success message or companies created
      // Note: This might show "already exist" if run multiple times
      final successFinder = find.textContaining('sample companies');
      final existsFinder = find.textContaining('already exist');
      expect(
        successFinder.evaluate().isNotEmpty || existsFinder.evaluate().isNotEmpty,
        isTrue,
      );
    });

    testWidgets('Test Company Creation Flow', (WidgetTester tester) async {
      // Start the app and login as Super Admin
      app.main();
      await tester.pumpAndSettle();
      await _loginAsSuperAdmin(tester);

      // Tap Create Company button
      await tester.tap(find.text('Create Company'));
      await tester.pumpAndSettle();

      // Verify Company Creation Screen
      expect(find.text('Create Company'), findsOneWidget);
      expect(find.text('Company Details'), findsOneWidget);
      expect(find.text('Location Details'), findsOneWidget);
      expect(find.text('Company Access Code'), findsOneWidget);

      // Fill in company details
      await tester.enterText(find.byType(TextFormField).at(0), 'Test Company Ltd');
      await tester.enterText(find.byType(TextFormField).at(1), '12345');
      await tester.enterText(find.byType(TextFormField).at(2), '123 Test Street, Test City');

      // Try to create company (might fail due to location requirement)
      await tester.tap(find.text('Create Company'));
      await tester.pumpAndSettle();
    });

    testWidgets('Test Access Code Management', (WidgetTester tester) async {
      // Start the app and login as Super Admin
      app.main();
      await tester.pumpAndSettle();
      await _loginAsSuperAdmin(tester);

      // Test Copy Access Code
      await tester.tap(find.byIcon(Icons.copy).first);
      await tester.pumpAndSettle();

      // Verify success message
      expect(find.textContaining('copied'), findsOneWidget);

      // Test Change Access Code
      await tester.tap(find.byIcon(Icons.more_vert));
      await tester.pumpAndSettle();
      await tester.tap(find.text('Change Access Code'));
      await tester.pumpAndSettle();

      // Verify Change Access Code Dialog
      expect(find.text('Change Access Code'), findsOneWidget);
      expect(find.text('Generate Random'), findsOneWidget);

      // Test Generate Random Code
      await tester.tap(find.text('Generate Random'));
      await tester.pumpAndSettle();

      // Cancel the dialog
      await tester.tap(find.text('Cancel'));
      await tester.pumpAndSettle();
    });

    testWidgets('Test Company List and Management', (WidgetTester tester) async {
      // Start the app and login as Super Admin
      app.main();
      await tester.pumpAndSettle();
      await _loginAsSuperAdmin(tester);

      // Create sample companies first
      await tester.tap(find.text('Sample Data'));
      await tester.pumpAndSettle();
      await tester.pump(const Duration(seconds: 3));
      await tester.pumpAndSettle();

      // Test View All Companies
      if (find.text('View All').evaluate().isNotEmpty) {
        await tester.tap(find.text('View All'));
        await tester.pumpAndSettle();

        // Verify All Companies Dialog
        expect(find.text('All Companies'), findsOneWidget);

        // Close dialog
        await tester.tap(find.byIcon(Icons.close));
        await tester.pumpAndSettle();
      }
    });

    testWidgets('Test Logout Functionality', (WidgetTester tester) async {
      // Start the app and login as Super Admin
      app.main();
      await tester.pumpAndSettle();
      await _loginAsSuperAdmin(tester);

      // Test Logout
      await tester.tap(find.byIcon(Icons.more_vert));
      await tester.pumpAndSettle();
      await tester.tap(find.text('Logout'));
      await tester.pumpAndSettle();

      // Confirm logout
      await tester.tap(find.text('Logout').last);
      await tester.pumpAndSettle();

      // Verify return to welcome screen
      expect(find.text('timeSync'), findsOneWidget);
      expect(find.text('Get Started'), findsOneWidget);
    });

    testWidgets('Test Clear Sample Data', (WidgetTester tester) async {
      // Start the app and login as Super Admin
      app.main();
      await tester.pumpAndSettle();
      await _loginAsSuperAdmin(tester);

      // Test Clear Sample Data
      await tester.tap(find.text('Clear Data'));
      await tester.pumpAndSettle();

      // Verify confirmation dialog
      expect(find.text('Clear Sample Data'), findsOneWidget);

      // Cancel first
      await tester.tap(find.text('Cancel'));
      await tester.pumpAndSettle();

      // Try again and confirm
      await tester.tap(find.text('Clear Data'));
      await tester.pumpAndSettle();
      await tester.tap(find.text('Delete'));
      await tester.pumpAndSettle();

      // Wait for operation to complete
      await tester.pump(const Duration(seconds: 2));
      await tester.pumpAndSettle();
    });
  });
}

// Helper function to test Super Admin access
Future<void> _testSuperAdminAccess(WidgetTester tester, String code, {required bool shouldSucceed}) async {
  // Navigate back to welcome screen if needed
  if (find.text('timeSync').evaluate().isEmpty) {
    await tester.pageBack();
    await tester.pumpAndSettle();
  }

  // Tap logo 5 times to access hidden Super Admin login
  final logoFinder = find.byIcon(Icons.access_time_rounded);
  for (int i = 0; i < 5; i++) {
    await tester.tap(logoFinder);
    await tester.pump(const Duration(milliseconds: 100));
  }
  await tester.pumpAndSettle();

  // Enter access code
  await tester.enterText(find.byType(TextField), code);
  await tester.tap(find.text('Verify Code'));
  await tester.pumpAndSettle();

  if (shouldSucceed) {
    // Should navigate to Super Admin Dashboard
    expect(find.text('Super Admin Dashboard'), findsOneWidget);
  } else {
    // Should show error message
    expect(find.textContaining('Invalid access code'), findsOneWidget);
    // Go back to welcome screen
    await tester.pageBack();
    await tester.pumpAndSettle();
  }
}

// Helper function to login as Super Admin
Future<void> _loginAsSuperAdmin(WidgetTester tester) async {
  // Tap logo 5 times
  final logoFinder = find.byIcon(Icons.access_time_rounded);
  for (int i = 0; i < 5; i++) {
    await tester.tap(logoFinder);
    await tester.pump(const Duration(milliseconds: 100));
  }
  await tester.pumpAndSettle();

  // Enter correct access code
  await tester.enterText(find.byType(TextField), 'ADMIN2024');
  await tester.tap(find.text('Verify Code'));
  await tester.pumpAndSettle();

  // Verify we're on the dashboard
  expect(find.text('Super Admin Dashboard'), findsOneWidget);
}
