import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:timesync/services/sample_data_service.dart';
import 'package:timesync/services/database_service.dart';
import 'package:timesync/models/company.dart';
import 'package:timesync/screens/company_owner/company_owner_dashboard_screen.dart';

void main() {
  group('Company Owner Unit Tests', () {
    late SampleDataService sampleDataService;
    late DatabaseService databaseService;

    setUpAll(() async {
      sampleDataService = SampleDataService();
      databaseService = DatabaseService();
    });

    test('Sample Data Service - Get Sample Company Codes', () {
      final codes = sampleDataService.getSampleCompanyCodes();
      
      expect(codes.length, 6);
      expect(codes.contains('APPLE2024'), true);
      expect(codes.contains('SAMSUNG24'), true);
      expect(codes.contains('GOOGLE2024'), true);
      expect(codes.contains('TCS2024'), true);
      expect(codes.contains('MSFT2024'), true);
      expect(codes.contains('META2024'), true);
    });

    test('Sample Data Service - Get Sample Company Data', () {
      final appleData = sampleDataService.getSampleCompanyData('Apple');
      final googleData = sampleDataService.getSampleCompanyData('Google');
      final tcsData = sampleDataService.getSampleCompanyData('Tata');
      
      expect(appleData, isNotNull);
      expect(appleData!['name'], 'Apple Inc.');
      expect(appleData['ownerCode'], 'APPLE2024');
      expect(appleData['pincode'], '95014');
      
      expect(googleData, isNotNull);
      expect(googleData!['name'], 'Google LLC');
      expect(googleData['ownerCode'], 'GOOGLE2024');
      
      expect(tcsData, isNotNull);
      expect(tcsData!['name'], 'Tata Consultancy Services');
      expect(tcsData['ownerCode'], 'TCS2024');
    });

    testWidgets('Company Owner Dashboard Widget Test', (WidgetTester tester) async {
      // Create a test company
      final testCompany = Company(
        id: 'test-id',
        name: 'Test Company Ltd',
        pincode: '12345',
        latitude: 37.4220,
        longitude: -122.0841,
        locationAddress: '123 Test Street, Test City',
        ownerCode: 'TEST2024',
        companyCode: 'TST24',
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
      );

      // Build the dashboard widget
      await tester.pumpWidget(
        MaterialApp(
          home: CompanyOwnerDashboardScreen(
            company: testCompany,
            accessCode: 'TEST2024',
          ),
        ),
      );

      // Verify basic elements are present
      expect(find.text('Test Company Ltd'), findsOneWidget);
      expect(find.text('Today\'s Overview'), findsOneWidget);
      expect(find.text('Total Employees'), findsOneWidget);
      expect(find.text('Present Today'), findsOneWidget);
      expect(find.text('Absent Today'), findsOneWidget);
      expect(find.text('Attendance Rate'), findsOneWidget);
      expect(find.text('Quick Actions'), findsOneWidget);
      expect(find.text('View Employees'), findsOneWidget);
      expect(find.text('Attendance Reports'), findsOneWidget);
      expect(find.text('Recent Employees'), findsOneWidget);
    });

    testWidgets('Company Owner Dashboard - Company Info Dialog', (WidgetTester tester) async {
      final testCompany = Company(
        id: 'test-id',
        name: 'Apple Inc.',
        pincode: '95014',
        latitude: 37.3318,
        longitude: -122.0312,
        locationAddress: '1 Apple Park Way, Cupertino, CA 95014, USA',
        ownerCode: 'APPLE2024',
        companyCode: 'APL24',
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
      );

      await tester.pumpWidget(
        MaterialApp(
          home: CompanyOwnerDashboardScreen(
            company: testCompany,
            accessCode: 'APPLE2024',
          ),
        ),
      );

      // Tap the menu button
      await tester.tap(find.byIcon(Icons.more_vert));
      await tester.pumpAndSettle();

      // Tap Company Info
      await tester.tap(find.text('Company Info'));
      await tester.pumpAndSettle();

      // Verify dialog content
      expect(find.text('Company Information'), findsOneWidget);
      expect(find.text('Apple Inc.'), findsOneWidget);
      expect(find.text('95014'), findsOneWidget);
      expect(find.textContaining('Cupertino'), findsOneWidget);
    });

    testWidgets('Company Owner Dashboard - Access Code Dialog', (WidgetTester tester) async {
      final testCompany = Company(
        id: 'test-id',
        name: 'Google LLC',
        pincode: '94043',
        latitude: 37.4220,
        longitude: -122.0841,
        locationAddress: '1600 Amphitheatre Parkway, Mountain View, CA 94043, USA',
        ownerCode: 'GOOGLE2024',
        companyCode: 'GOO24',
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
      );

      await tester.pumpWidget(
        MaterialApp(
          home: CompanyOwnerDashboardScreen(
            company: testCompany,
            accessCode: 'GOOGLE2024',
          ),
        ),
      );

      // Tap the menu button
      await tester.tap(find.byIcon(Icons.more_vert));
      await tester.pumpAndSettle();

      // Tap Access Code
      await tester.tap(find.text('Access Code'));
      await tester.pumpAndSettle();

      // Verify dialog content
      expect(find.text('Company Access Code'), findsOneWidget);
      expect(find.text('GOOGLE2024'), findsOneWidget);
      expect(find.byIcon(Icons.copy), findsOneWidget);
      expect(find.textContaining('Share this code'), findsOneWidget);
    });

    test('Company Owner Access Codes - Case Sensitivity', () {
      final codes = sampleDataService.getSampleCompanyCodes();
      
      // Verify all codes are uppercase
      for (final code in codes) {
        expect(code, equals(code.toUpperCase()));
        expect(code, isNot(equals(code.toLowerCase())));
      }
    });

    test('Sample Company Data Validation', () {
      final companies = [
        'Apple',
        'Samsung',
        'Google',
        'Tata',
        'Microsoft',
        'Meta',
      ];

      for (final companyName in companies) {
        final data = sampleDataService.getSampleCompanyData(companyName);
        
        expect(data, isNotNull, reason: 'Company data should exist for $companyName');
        expect(data!['name'], isNotEmpty, reason: 'Company name should not be empty');
        expect(data['ownerCode'], isNotEmpty, reason: 'Owner code should not be empty');
        expect(data['pincode'], isNotEmpty, reason: 'Pincode should not be empty');
        expect(data['locationAddress'], isNotEmpty, reason: 'Address should not be empty');
        expect(data['latitude'], isA<double>(), reason: 'Latitude should be a double');
        expect(data['longitude'], isA<double>(), reason: 'Longitude should be a double');
      }
    });
  });
}
