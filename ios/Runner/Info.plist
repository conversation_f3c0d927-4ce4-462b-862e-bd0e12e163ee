<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
	<dict>
		<key>CFBundleDevelopmentRegion</key>
		<string>$(DEVELOPMENT_LANGUAGE)</string>
		<key>CFBundleDisplayName</key>
		<string>timeSync</string>
		<key>CFBundleExecutable</key>
		<string>$(EXECUTABLE_NAME)</string>
		<key>CFBundleIdentifier</key>
		<string>$(PRODUCT_BUNDLE_IDENTIFIER)</string>
		<key>CFBundleInfoDictionaryVersion</key>
		<string>6.0</string>
		<key>CFBundleName</key>
		<string>timeSync</string>
		<key>CFBundlePackageType</key>
		<string>APPL</string>
		<key>CFBundleShortVersionString</key>
		<string>$(FLUTTER_BUILD_NAME)</string>
		<key>CFBundleSignature</key>
		<string>????</string>
		<key>CFBundleVersion</key>
		<string>$(FLUTTER_BUILD_NUMBER)</string>
		<key>LSRequiresIPhoneOS</key>
		<true/>
		<key>UILaunchStoryboardName</key>
		<string>LaunchScreen</string>
		<key>UIMainStoryboardFile</key>
		<string>Main</string>
		<key>UISupportedInterfaceOrientations</key>
		<array>
			<string>UIInterfaceOrientationPortrait</string>
			<string>UIInterfaceOrientationLandscapeLeft</string>
			<string>UIInterfaceOrientationLandscapeRight</string>
		</array>
		<key>UISupportedInterfaceOrientations~ipad</key>
		<array>
			<string>UIInterfaceOrientationPortrait</string>
			<string>UIInterfaceOrientationPortraitUpsideDown</string>
			<string>UIInterfaceOrientationLandscapeLeft</string>
			<string>UIInterfaceOrientationLandscapeRight</string>
		</array>
		<key>CADisableMinimumFrameDurationOnPhone</key>
		<true/>
		<key>UIApplicationSupportsIndirectInputEvents</key>
		<true/>
		<key>UIStatusBarHidden</key>
		<false/>

		<!-- Location permissions -->
		<key>NSLocationWhenInUseUsageDescription</key>
		<string>timeSync needs location access to provide accurate time synchronization based on your location.</string>
		<key>NSLocationAlwaysAndWhenInUseUsageDescription</key>
		<string>timeSync needs location access to provide accurate time synchronization even when the app is in the background.</string>
		<key>NSLocationAlwaysUsageDescription</key>
		<string>timeSync needs location access to provide accurate time synchronization even when the app is in the background.</string>

		<!-- Background modes -->
		<key>UIBackgroundModes</key>
		<array>
			<string>location</string>
			<string>background-fetch</string>
			<string>background-processing</string>
		</array>

		<!-- Network usage description -->
		<key>NSNetworkUsageDescription</key>
		<string>timeSync needs network access to synchronize time with remote servers.</string>

		<!-- Notification permissions -->
		<key>NSUserNotificationUsageDescription</key>
		<string>timeSync needs notification access to alert you about time synchronization events.</string>

		<!-- Background app refresh -->
		<key>UIBackgroundRefreshStatusDidChangeNotification</key>
		<true/>

	</dict>
</plist>
