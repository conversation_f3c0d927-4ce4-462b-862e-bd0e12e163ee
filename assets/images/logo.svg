<?xml version="1.0" encoding="UTF-8"?>
<svg width="200" height="200" viewBox="0 0 200 200" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <linearGradient id="gradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#667eea;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#764ba2;stop-opacity:1" />
    </linearGradient>
  </defs>
  
  <!-- Background circle -->
  <circle cx="100" cy="100" r="90" fill="url(#gradient)" stroke="#ffffff" stroke-width="4"/>
  
  <!-- Clock face -->
  <circle cx="100" cy="100" r="70" fill="none" stroke="#ffffff" stroke-width="2" opacity="0.3"/>
  
  <!-- Clock hands -->
  <line x1="100" y1="100" x2="100" y2="50" stroke="#ffffff" stroke-width="4" stroke-linecap="round"/>
  <line x1="100" y1="100" x2="130" y2="100" stroke="#ffffff" stroke-width="3" stroke-linecap="round"/>
  
  <!-- Center dot -->
  <circle cx="100" cy="100" r="6" fill="#ffffff"/>
  
  <!-- Sync arrows -->
  <path d="M 60 40 L 70 30 L 70 35 L 85 35 L 85 45 L 70 45 L 70 50 Z" fill="#ffffff" opacity="0.8"/>
  <path d="M 140 160 L 130 170 L 130 165 L 115 165 L 115 155 L 130 155 L 130 150 Z" fill="#ffffff" opacity="0.8"/>
  
  <!-- Text -->
  <text x="100" y="180" text-anchor="middle" font-family="Arial, sans-serif" font-size="16" font-weight="bold" fill="#ffffff">timeSync</text>
</svg>
