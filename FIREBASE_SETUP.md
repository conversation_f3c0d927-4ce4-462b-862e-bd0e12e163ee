# Firebase Setup Guide for timeSync

## Prerequisites
1. Create a Firebase project at [Firebase Console](https://console.firebase.google.com/)
2. Install Firebase CLI: `npm install -g firebase-tools`
3. Install FlutterFire CLI: `dart pub global activate flutterfire_cli`

## Setup Steps

### 1. Create Firebase Project
1. Go to [Firebase Console](https://console.firebase.google.com/)
2. Click "Create a project"
3. Enter project name: `timesync-app` (or your preferred name)
4. Enable Google Analytics (recommended)
5. Choose or create a Google Analytics account

### 2. Configure Firebase for Flutter
Run the following command in your project root:
```bash
flutterfire configure
```

This will:
- Create/update `lib/firebase_options.dart`
- Add platform-specific configuration files
- Register your app with Firebase

### 3. Enable Firebase Services

#### Authentication
1. Go to Firebase Console → Authentication
2. Click "Get started"
3. Go to "Sign-in method" tab
4. Enable "Anonymous" authentication
5. Optionally enable "Email/Password" for future use

#### Firestore Database
1. Go to Firebase Console → Firestore Database
2. Click "Create database"
3. <PERSON>ose "Start in test mode" (for development)
4. Select a location close to your users

#### Cloud Messaging (FCM)
1. Go to Firebase Console → Cloud Messaging
2. No additional setup required - automatically enabled

#### Analytics
1. Go to Firebase Console → Analytics
2. Should be automatically enabled if you chose it during project creation

#### Crashlytics
1. Go to Firebase Console → Crashlytics
2. Click "Get started"
3. Follow the setup instructions

#### Storage
1. Go to Firebase Console → Storage
2. Click "Get started"
3. Choose "Start in test mode"
4. Select a location

#### Remote Config
1. Go to Firebase Console → Remote Config
2. Click "Create configuration"
3. Add default parameters as needed

### 4. Android Configuration

#### Update build.gradle (Project level)
File: `android/build.gradle`
```gradle
buildscript {
    dependencies {
        classpath 'com.google.gms:google-services:4.4.0'
        classpath 'com.google.firebase:firebase-crashlytics-gradle:2.9.9'
    }
}
```

#### Update build.gradle (App level)
File: `android/app/build.gradle.kts`
Add at the top:
```kotlin
plugins {
    id("com.google.gms.google-services")
    id("com.google.firebase.crashlytics")
}
```

#### Add google-services.json
1. Download `google-services.json` from Firebase Console
2. Place it in `android/app/` directory

### 5. iOS Configuration

#### Add GoogleService-Info.plist
1. Download `GoogleService-Info.plist` from Firebase Console
2. Add it to `ios/Runner/` directory
3. Add it to Xcode project (drag & drop into Runner folder)

#### Update Info.plist
File: `ios/Runner/Info.plist`
Add before `</dict>`:
```xml
<key>CFBundleURLTypes</key>
<array>
    <dict>
        <key>CFBundleURLName</key>
        <string>REVERSED_CLIENT_ID</string>
        <key>CFBundleURLSchemes</key>
        <array>
            <string>YOUR_REVERSED_CLIENT_ID</string>
        </array>
    </dict>
</array>
```

### 6. Security Rules

#### Firestore Rules
```javascript
rules_version = '2';
service cloud.firestore {
  match /databases/{database}/documents {
    // Users can only access their own data
    match /users/{userId} {
      allow read, write: if request.auth != null && request.auth.uid == userId;
      
      // Allow access to subcollections
      match /{document=**} {
        allow read, write: if request.auth != null && request.auth.uid == userId;
      }
    }
    
    // Public app configuration
    match /config/{document} {
      allow read: if true;
      allow write: if false;
    }
    
    // Analytics data (write-only for users)
    match /analytics/{document} {
      allow read: if false;
      allow write: if request.auth != null;
    }
  }
}
```

#### Storage Rules
```javascript
rules_version = '2';
service firebase.storage {
  match /b/{bucket}/o {
    match /users/{userId}/{allPaths=**} {
      allow read, write: if request.auth != null && request.auth.uid == userId;
    }
  }
}
```

### 7. Test Firebase Integration
1. Run `flutter clean`
2. Run `flutter pub get`
3. Run `flutter run`
4. Check Firebase Console for:
   - Anonymous user in Authentication
   - Analytics events
   - Crashlytics (if any errors occur)

### 8. Environment Variables (Optional)
For multiple environments (dev, staging, prod), you can:
1. Create separate Firebase projects
2. Use different `google-services.json` files
3. Configure build variants in Android
4. Use different schemes in iOS

## Troubleshooting

### Common Issues
1. **Build errors**: Ensure all configuration files are in correct locations
2. **Authentication fails**: Check if Anonymous auth is enabled
3. **Firestore permission denied**: Verify security rules
4. **iOS build fails**: Ensure GoogleService-Info.plist is added to Xcode project

### Debug Commands
```bash
# Check Firebase configuration
flutterfire configure

# Clean and rebuild
flutter clean
flutter pub get
flutter run

# Check Firebase CLI
firebase --version
```

## Next Steps
1. Set up proper security rules for production
2. Configure app distribution
3. Set up CI/CD with Firebase
4. Monitor app performance with Firebase Performance
5. Set up A/B testing with Remote Config
