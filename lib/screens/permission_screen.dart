import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import '../services/permission_manager.dart';
import '../services/auth_service.dart';
import '../services/data_service.dart';

class PermissionScreen extends StatefulWidget {
  final VoidCallback onPermissionsGranted;

  const PermissionScreen({super.key, required this.onPermissionsGranted});

  @override
  State<PermissionScreen> createState() => _PermissionScreenState();
}

class _PermissionScreenState extends State<PermissionScreen>
    with TickerProviderStateMixin {
  final PermissionManager _permissionManager = PermissionManager();
  final AuthService _authService = AuthService();
  final DataService _dataService = DataService();
  bool _isLoading = false;
  bool _permissionsChecked = false;
  
  late AnimationController _fadeController;
  late AnimationController _slideController;
  late Animation<double> _fadeAnimation;
  late Animation<Offset> _slideAnimation;

  @override
  void initState() {
    super.initState();
    
    _fadeController = AnimationController(
      duration: const Duration(milliseconds: 1000),
      vsync: this,
    );
    
    _slideController = AnimationController(
      duration: const Duration(milliseconds: 800),
      vsync: this,
    );
    
    _fadeAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _fadeController,
      curve: Curves.easeInOut,
    ));
    
    _slideAnimation = Tween<Offset>(
      begin: const Offset(0, 0.3),
      end: Offset.zero,
    ).animate(CurvedAnimation(
      parent: _slideController,
      curve: Curves.easeOutCubic,
    ));
    
    _startAnimations();
    _setPermissionsChecked();
  }

  void _startAnimations() {
    _fadeController.forward();
    Future.delayed(const Duration(milliseconds: 300), () {
      _slideController.forward();
    });
  }

  void _setPermissionsChecked() async {
    await Future.delayed(const Duration(milliseconds: 1500)); // Let animations play

    setState(() {
      _permissionsChecked = true;
    });
  }

  Future<void> _requestPermissions() async {
    setState(() {
      _isLoading = true;
    });

    try {
      // Initialize notifications first
      await _permissionManager.initializeNotifications();
      
      // Request all permissions
      if (!mounted) return;
      final allGranted = await _permissionManager.requestAllPermissions(context);

      if (allGranted) {
        // Request battery optimization exemption
        await _permissionManager.requestBatteryOptimizationExemption();

        // Sign in user anonymously to Firebase
        final authResult = await _authService.signInAnonymously();
        if (authResult.isSuccess) {
          // Save initial app usage data
          await _dataService.saveAppUsageData('permissions_granted_first_time');

          // Show success feedback
          HapticFeedback.lightImpact();

          // Proceed to main app
          if (mounted) {
            widget.onPermissionsGranted();
          }
        } else {
          // Continue without Firebase authentication
          debugPrint('Firebase auth failed: ${authResult.error}');
          if (mounted) {
            widget.onPermissionsGranted();
          }
        }
      } else {
        // Show error message
        if (mounted) {
          _showErrorSnackBar();
        }
      }
    } catch (e) {
      debugPrint('Error requesting permissions: $e');
      _showErrorSnackBar();
    } finally {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  void _showErrorSnackBar() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('Some permissions were not granted. The app may not function properly.'),
        backgroundColor: Colors.red,
        duration: Duration(seconds: 3),
      ),
    );
  }

  @override
  void dispose() {
    _fadeController.dispose();
    _slideController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Container(
        decoration: const BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.topLeft,
            end: Alignment.bottomRight,
            colors: [
              Color(0xFF667eea),
              Color(0xFF764ba2),
            ],
          ),
        ),
        child: SafeArea(
          child: Padding(
            padding: const EdgeInsets.all(24.0),
            child: Column(
              children: [
                Expanded(
                  child: FadeTransition(
                    opacity: _fadeAnimation,
                    child: SlideTransition(
                      position: _slideAnimation,
                      child: SingleChildScrollView(
                        child: Column(
                          mainAxisAlignment: MainAxisAlignment.center,
                          children: [
                          // App icon
                          Container(
                            width: 100,
                            height: 100,
                            decoration: BoxDecoration(
                              color: Colors.white,
                              shape: BoxShape.circle,
                              boxShadow: [
                                BoxShadow(
                                  color: Colors.black.withValues(alpha: 0.2),
                                  blurRadius: 20,
                                  offset: const Offset(0, 10),
                                ),
                              ],
                            ),
                            child: const Icon(
                              Icons.access_time_rounded,
                              size: 50,
                              color: Color(0xFF667eea),
                            ),
                          ),
                          
                          const SizedBox(height: 40),
                          
                          // Title
                          const Text(
                            'timeSync Permissions',
                            style: TextStyle(
                              fontSize: 28,
                              fontWeight: FontWeight.bold,
                              color: Colors.white,
                            ),
                            textAlign: TextAlign.center,
                          ),
                          
                          const SizedBox(height: 16),
                          
                          // Subtitle
                          const Text(
                            'To provide the best experience, timeSync needs access to:',
                            style: TextStyle(
                              fontSize: 16,
                              color: Colors.white70,
                            ),
                            textAlign: TextAlign.center,
                          ),
                          
                          const SizedBox(height: 30),

                          // Permission items
                          _buildPermissionItem(
                            Icons.location_on,
                            'Location Access',
                            'For accurate time synchronization based on your location',
                          ),

                          const SizedBox(height: 12),

                          _buildPermissionItem(
                            Icons.location_history,
                            'Background Location',
                            'To sync time even when the app is closed',
                          ),

                          const SizedBox(height: 12),

                          _buildPermissionItem(
                            Icons.notifications,
                            'Notifications',
                            'To alert you about time synchronization events',
                          ),

                          const SizedBox(height: 12),

                          _buildPermissionItem(
                            Icons.wifi,
                            'Internet Access',
                            'To connect to time servers for synchronization',
                          ),

                          const SizedBox(height: 20),
                          ],
                        ),
                      ),
                    ),
                  ),
                ),
                
                // Action button
                if (_permissionsChecked)
                  FadeTransition(
                    opacity: _fadeAnimation,
                    child: SizedBox(
                      width: double.infinity,
                      height: 56,
                      child: ElevatedButton(
                        onPressed: _isLoading ? null : _requestPermissions,
                        style: ElevatedButton.styleFrom(
                          backgroundColor: Colors.white,
                          foregroundColor: const Color(0xFF667eea),
                          shape: RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(28),
                          ),
                          elevation: 8,
                        ),
                        child: _isLoading
                            ? const SizedBox(
                                width: 24,
                                height: 24,
                                child: CircularProgressIndicator(
                                  strokeWidth: 2,
                                  valueColor: AlwaysStoppedAnimation<Color>(
                                    Color(0xFF667eea),
                                  ),
                                ),
                              )
                            : const Text(
                                'Grant Permissions',
                                style: TextStyle(
                                  fontSize: 18,
                                  fontWeight: FontWeight.bold,
                                ),
                              ),
                      ),
                    ),
                  ),
                
                const SizedBox(height: 20),
                
                // Privacy note
                FadeTransition(
                  opacity: _fadeAnimation,
                  child: const Text(
                    'Your privacy is important to us. These permissions are only used for app functionality.',
                    style: TextStyle(
                      fontSize: 12,
                      color: Colors.white60,
                    ),
                    textAlign: TextAlign.center,
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildPermissionItem(IconData icon, String title, String description) {
    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: Colors.white.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: Colors.white.withValues(alpha: 0.2),
          width: 1,
        ),
      ),
      child: Row(
        children: [
          Container(
            width: 40,
            height: 40,
            decoration: BoxDecoration(
              color: Colors.white.withValues(alpha: 0.2),
              borderRadius: BorderRadius.circular(20),
            ),
            child: Icon(
              icon,
              color: Colors.white,
              size: 20,
            ),
          ),
          const SizedBox(width: 12),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  title,
                  style: const TextStyle(
                    fontSize: 14,
                    fontWeight: FontWeight.bold,
                    color: Colors.white,
                  ),
                ),
                const SizedBox(height: 2),
                Text(
                  description,
                  style: const TextStyle(
                    fontSize: 12,
                    color: Colors.white70,
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }
}
