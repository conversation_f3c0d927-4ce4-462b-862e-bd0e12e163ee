import 'package:flutter/material.dart';

import 'package:firebase_auth/firebase_auth.dart';
import '../../services/database_service.dart';
import '../../services/navigation_service.dart';
import '../../services/network_service.dart';
import '../../models/company.dart';
import '../../models/employee.dart';

class EmployeeOnboardingScreen extends StatefulWidget {
  const EmployeeOnboardingScreen({super.key});

  @override
  State<EmployeeOnboardingScreen> createState() => _EmployeeOnboardingScreenState();
}

class _EmployeeOnboardingScreenState extends State<EmployeeOnboardingScreen> {
  final _formKey = GlobalKey<FormState>();
  final _nameController = TextEditingController();
  final _phoneNumberController = TextEditingController();
  final _passwordController = TextEditingController();
  final _designationController = TextEditingController();
  final _companyCodeController = TextEditingController();

  final DatabaseService _databaseService = DatabaseService();
  final FirebaseAuth _auth = FirebaseAuth.instance;
  final NetworkService _networkService = NetworkService();

  Company? _selectedCompany; // Will be set after company code verification
  bool _isLoading = false;
  bool _obscurePassword = true;
  int _currentStep = 0;

  @override
  void dispose() {
    _nameController.dispose();
    _phoneNumberController.dispose();
    _passwordController.dispose();
    _designationController.dispose();
    _companyCodeController.dispose();
    super.dispose();
  }

  Future<void> _verifyCompanyCode() async {
    final companyCode = _companyCodeController.text.trim().toUpperCase();

    debugPrint('🔍 COMPANY CODE: Starting verification for code: "$companyCode"');

    if (companyCode.isEmpty) {
      debugPrint('❌ COMPANY CODE: Empty code provided');
      _showError('Please enter a company code');
      return;
    }

    setState(() {
      _isLoading = true;
      _selectedCompany = null;
    });

    try {
      debugPrint('🔍 COMPANY CODE: Searching for company with code: $companyCode');
      final company = await _databaseService.getCompanyByCompanyCode(companyCode);
      debugPrint('🔍 COMPANY CODE: Company lookup result: ${company?.name ?? 'null'}');

      if (company != null) {
        debugPrint('✅ COMPANY CODE: Company found: ${company.name}');
        setState(() {
          _selectedCompany = company;
        });
        _showSuccess('Company verified: ${company.name}');
      } else {
        debugPrint('❌ COMPANY CODE: No company found with code: $companyCode');
        _showError('Invalid company code. Please check and try again.');
      }
    } catch (e) {
      debugPrint('❌ COMPANY CODE: Exception during verification: $e');
      _showError('Failed to verify company code. Please try again.');
    } finally {
      debugPrint('🔍 COMPANY CODE: Verification process completed');
      setState(() {
        _isLoading = false;
      });
    }
  }

  Future<void> _registerEmployee() async {
    if (!_formKey.currentState!.validate() || _selectedCompany == null) {
      _showError('Please fill all fields and select a company');
      return;
    }

    setState(() {
      _isLoading = true;
    });

    // Check network connectivity first
    if (!_networkService.isConnected) {
      _showError('No internet connection. Please check your network and try again.');
      setState(() {
        _isLoading = false;
      });
      return;
    }

    // Test actual internet connectivity
    final hasInternet = await _networkService.hasInternetConnection();
    if (!hasInternet) {
      _showError('Unable to connect to the internet. Please check your connection and try again.');
      setState(() {
        _isLoading = false;
      });
      return;
    }

    try {
      // Create Firebase Auth user with retry logic
      UserCredential? userCredential;
      int retryCount = 0;
      const maxRetries = 3;

      // Generate temporary email from phone number for Firebase Auth
      final phoneNumber = _phoneNumberController.text.trim();
      final tempEmail = '${phoneNumber.replaceAll(RegExp(r'[^0-9]'), '')}@timesync.temp';

      debugPrint('🔍 EMPLOYEE REGISTRATION: Using phone: $phoneNumber, temp email: $tempEmail');

      while (retryCount < maxRetries) {
        try {
          userCredential = await _auth.createUserWithEmailAndPassword(
            email: tempEmail,
            password: _passwordController.text.trim(),
          );
          break; // Success, exit retry loop
        } on FirebaseAuthException catch (e) {
          if (e.code == 'network-request-failed' && retryCount < maxRetries - 1) {
            retryCount++;
            _showError('Network error. Retrying... (${retryCount}/$maxRetries)');
            await Future.delayed(Duration(seconds: retryCount * 2)); // Exponential backoff
            continue;
          } else {
            rethrow; // Re-throw if not a network error or max retries reached
          }
        }
      }

      if (userCredential?.user != null) {
        // Update display name
        await userCredential!.user!.updateDisplayName(_nameController.text.trim());

        // Create employee record in Firestore
        final employee = Employee(
          id: userCredential.user!.uid,
          name: _nameController.text.trim(),
          phoneNumber: _phoneNumberController.text.trim(),
          designation: _designationController.text.trim(),
          email: tempEmail, // Store temp email for Firebase Auth compatibility
          companyId: _selectedCompany!.id,
          companyName: _selectedCompany!.name,
          isActive: true,
          createdAt: DateTime.now(),
          updatedAt: DateTime.now(),
        );

        final success = await _databaseService.createEmployee(employee);

        if (success) {
          _showSuccessDialog(employee);
        } else {
          // If Firestore creation fails, delete the Auth user
          try {
            await userCredential.user!.delete();
          } catch (deleteError) {
            debugPrint('Failed to delete user after Firestore error: $deleteError');
          }
          _showError('Failed to create employee record. Please try again.');
        }
      } else {
        _showError('Failed to create user account. Please try again.');
      }
    } on FirebaseAuthException catch (e) {
      String errorMessage = 'Registration failed';
      switch (e.code) {
        case 'weak-password':
          errorMessage = 'Password is too weak. Please use at least 6 characters.';
          break;
        case 'email-already-in-use':
          errorMessage = 'An account already exists with this email.';
          break;
        case 'invalid-email':
          errorMessage = 'Please enter a valid email address.';
          break;
        case 'network-request-failed':
          errorMessage = 'Network error. Please check your internet connection and try again.';
          break;
        case 'too-many-requests':
          errorMessage = 'Too many attempts. Please wait a moment and try again.';
          break;
        default:
          errorMessage = 'Registration failed: ${e.message ?? 'Unknown error'}';
      }
      _showError(errorMessage);
    } catch (e) {
      _showError('Registration failed. Please check your internet connection and try again.');
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  void _showError(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: Colors.red,
      ),
    );
  }

  void _showSuccess(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: Colors.green,
      ),
    );
  }

  void _showSuccessDialog(Employee employee) {
    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (context) => AlertDialog(
        title: Row(
          children: [
            Icon(Icons.check_circle, color: Colors.green, size: 28),
            SizedBox(width: 12),
            Text('Welcome!'),
          ],
        ),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Welcome to ${employee.companyName}, ${employee.name}!',
              style: TextStyle(fontSize: 16),
            ),
            SizedBox(height: 16),
            Text(
              'Your employee account has been created successfully. You can now check in and track your attendance.',
              style: TextStyle(fontSize: 14, color: Colors.grey[600]),
            ),
          ],
        ),
        actions: [
          ElevatedButton(
            onPressed: () {
              Navigator.of(context).pop();
              NavigationService().navigateToEmployeeDashboard();
            },
            child: Text('Get Started'),
          ),
        ],
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.grey[50],
      appBar: AppBar(
        title: const Text('Employee Registration'),
        backgroundColor: const Color(0xFF667eea),
        foregroundColor: Colors.white,
        elevation: 0,
      ),
      body: SingleChildScrollView(
              padding: const EdgeInsets.all(16),
              child: Form(
                key: _formKey,
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    _buildHeaderCard(),
                    const SizedBox(height: 24),
                    _buildStepIndicator(),
                    const SizedBox(height: 24),
                    _buildCurrentStep(),
                    const SizedBox(height: 32),
                    _buildNavigationButtons(),
                  ],
                ),
              ),
            ),
    );
  }

  Widget _buildHeaderCard() {
    return Container(
      width: double.infinity,
      padding: const EdgeInsets.all(24),
      decoration: BoxDecoration(
        gradient: const LinearGradient(
          colors: [Color(0xFF667eea), Color(0xFF764ba2)],
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
        ),
        borderRadius: BorderRadius.circular(20),
        boxShadow: [
          BoxShadow(
            color: const Color(0xFF667eea).withOpacity(0.3),
            blurRadius: 20,
            offset: const Offset(0, 10),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Container(
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  color: Colors.white.withOpacity(0.2),
                  borderRadius: BorderRadius.circular(12),
                ),
                child: const Icon(
                  Icons.person_add,
                  color: Colors.white,
                  size: 28,
                ),
              ),
              const SizedBox(width: 16),
              const Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'Join Your Team',
                      style: TextStyle(
                        color: Colors.white,
                        fontSize: 24,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    SizedBox(height: 4),
                    Text(
                      'Create your employee account',
                      style: TextStyle(
                        color: Colors.white70,
                        fontSize: 16,
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildStepIndicator() {
    return Row(
      children: [
        for (int i = 0; i < 3; i++)
          Expanded(
            child: Container(
              margin: EdgeInsets.only(right: i < 2 ? 8 : 0),
              height: 4,
              decoration: BoxDecoration(
                color: i <= _currentStep ? const Color(0xFF667eea) : Colors.grey[300],
                borderRadius: BorderRadius.circular(2),
              ),
            ),
          ),
      ],
    );
  }

  Widget _buildCurrentStep() {
    switch (_currentStep) {
      case 0:
        return _buildPersonalInfoStep();
      case 1:
        return _buildCompanySelectionStep();
      case 2:
        return _buildAccountSetupStep();
      default:
        return _buildPersonalInfoStep();
    }
  }

  Widget _buildPersonalInfoStep() {
    return Container(
      padding: const EdgeInsets.all(24),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.05),
            blurRadius: 10,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Text(
            'Personal Information',
            style: TextStyle(
              fontSize: 20,
              fontWeight: FontWeight.bold,
              color: Colors.black87,
            ),
          ),
          const SizedBox(height: 20),
          TextFormField(
            controller: _nameController,
            decoration: const InputDecoration(
              labelText: 'Full Name',
              hintText: 'Enter your full name',
              border: OutlineInputBorder(),
              prefixIcon: Icon(Icons.person),
            ),
            validator: (value) {
              if (value == null || value.trim().isEmpty) {
                return 'Please enter your full name';
              }
              return null;
            },
          ),
          const SizedBox(height: 16),
          TextFormField(
            controller: _designationController,
            decoration: const InputDecoration(
              labelText: 'Designation',
              hintText: 'Enter your job title',
              border: OutlineInputBorder(),
              prefixIcon: Icon(Icons.work),
            ),
            validator: (value) {
              if (value == null || value.trim().isEmpty) {
                return 'Please enter your designation';
              }
              return null;
            },
          ),
        ],
      ),
    );
  }

  Widget _buildCompanySelectionStep() {
    return Container(
      padding: const EdgeInsets.all(24),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.05),
            blurRadius: 10,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Text(
            'Enter Company Code',
            style: TextStyle(
              fontSize: 20,
              fontWeight: FontWeight.bold,
              color: Colors.black87,
            ),
          ),
          const SizedBox(height: 8),
          Text(
            'Enter the company code provided by your employer',
            style: TextStyle(
              fontSize: 14,
              color: Colors.grey[600],
            ),
          ),
          const SizedBox(height: 20),

          // Company Code Input Field
          TextFormField(
            controller: _companyCodeController,
            decoration: const InputDecoration(
              labelText: 'Company Code',
              hintText: 'Enter your company code',
              prefixIcon: Icon(Icons.business_outlined),
              border: OutlineInputBorder(
                borderRadius: BorderRadius.all(Radius.circular(12)),
              ),
              filled: true,
              fillColor: Colors.white,
            ),
            textCapitalization: TextCapitalization.characters,
            onChanged: (value) {
              debugPrint('🔍 COMPANY CODE INPUT: Code changed to: "$value"');
              // Clear selected company when code changes
              if (_selectedCompany != null) {
                setState(() {
                  _selectedCompany = null;
                });
              }
            },
            validator: (value) {
              if (value == null || value.trim().isEmpty) {
                return 'Please enter your company code';
              }
              return null;
            },
          ),

          const SizedBox(height: 16),

          // Verify Company Code Button
          SizedBox(
            width: double.infinity,
            child: ElevatedButton.icon(
              onPressed: _isLoading ? null : _verifyCompanyCode,
              icon: const Icon(Icons.search, size: 18),
              label: const Text('Verify Company Code'),
              style: ElevatedButton.styleFrom(
                backgroundColor: const Color(0xFF667eea),
                foregroundColor: Colors.white,
                padding: const EdgeInsets.symmetric(vertical: 12),
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(12),
                ),
              ),
            ),
          ),

          if (_selectedCompany != null) ...[
            const SizedBox(height: 16),
            Container(
              padding: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                color: Colors.green.withValues(alpha: 0.1),
                borderRadius: BorderRadius.circular(12),
                border: Border.all(color: Colors.green.withValues(alpha: 0.3)),
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(
                    children: [
                      Icon(Icons.check_circle, color: Colors.green[600], size: 20),
                      const SizedBox(width: 8),
                      const Text(
                        'Company Verified',
                        style: TextStyle(
                          fontSize: 16,
                          fontWeight: FontWeight.w600,
                          color: Colors.green,
                        ),
                      ),
                    ],
                  ),
                  const SizedBox(height: 8),
                  Text(
                    _selectedCompany!.name,
                    style: const TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                  const SizedBox(height: 4),
                  Text(
                    _selectedCompany!.locationAddress,
                    style: TextStyle(
                      fontSize: 14,
                      color: Colors.grey[600],
                    ),
                  ),
                ],
              ),
            ),
          ],
        ],
      ),
    );
  }

  Widget _buildAccountSetupStep() {
    return Container(
      padding: const EdgeInsets.all(24),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.05),
            blurRadius: 10,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Text(
            'Account Setup',
            style: TextStyle(
              fontSize: 20,
              fontWeight: FontWeight.bold,
              color: Colors.black87,
            ),
          ),
          const SizedBox(height: 20),
          TextFormField(
            controller: _phoneNumberController,
            keyboardType: TextInputType.phone,
            decoration: const InputDecoration(
              labelText: 'Phone Number',
              hintText: 'Enter your phone number',
              border: OutlineInputBorder(),
              prefixIcon: Icon(Icons.phone),
            ),
            onChanged: (value) {
              debugPrint('🔍 PHONE INPUT: Phone number changed to: "$value"');
            },
            validator: (value) {
              if (value == null || value.trim().isEmpty) {
                return 'Please enter your phone number';
              }
              // Basic phone number validation (can be enhanced)
              if (!RegExp(r'^[\+]?[0-9]{10,15}$').hasMatch(value.replaceAll(RegExp(r'[\s\-\(\)]'), ''))) {
                return 'Please enter a valid phone number';
              }
              return null;
            },
          ),
          const SizedBox(height: 16),
          TextFormField(
            controller: _passwordController,
            obscureText: _obscurePassword,
            decoration: InputDecoration(
              labelText: 'Password',
              hintText: 'Enter your password',
              border: const OutlineInputBorder(),
              prefixIcon: const Icon(Icons.lock),
              suffixIcon: IconButton(
                icon: Icon(_obscurePassword ? Icons.visibility : Icons.visibility_off),
                onPressed: () {
                  setState(() {
                    _obscurePassword = !_obscurePassword;
                  });
                },
              ),
            ),
            validator: (value) {
              if (value == null || value.trim().isEmpty) {
                return 'Please enter a password';
              }
              if (value.length < 6) {
                return 'Password must be at least 6 characters';
              }
              return null;
            },
          ),
        ],
      ),
    );
  }

  Widget _buildNavigationButtons() {
    return Row(
      children: [
        if (_currentStep > 0)
          Expanded(
            child: OutlinedButton(
              onPressed: () {
                setState(() {
                  _currentStep--;
                });
              },
              style: OutlinedButton.styleFrom(
                foregroundColor: const Color(0xFF667eea),
                side: const BorderSide(color: Color(0xFF667eea)),
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(12),
                ),
                padding: const EdgeInsets.symmetric(vertical: 16),
              ),
              child: const Text('Previous'),
            ),
          ),
        if (_currentStep > 0) const SizedBox(width: 16),
        Expanded(
          child: ElevatedButton(
            onPressed: _isLoading ? null : _handleNextStep,
            style: ElevatedButton.styleFrom(
              backgroundColor: const Color(0xFF667eea),
              foregroundColor: Colors.white,
              elevation: 0,
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(12),
              ),
              padding: const EdgeInsets.symmetric(vertical: 16),
            ),
            child: _isLoading
                ? const SizedBox(
                    width: 20,
                    height: 20,
                    child: CircularProgressIndicator(
                      valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                      strokeWidth: 2,
                    ),
                  )
                : Text(_currentStep == 2 ? 'Create Account' : 'Next'),
          ),
        ),
      ],
    );
  }

  void _handleNextStep() {
    if (_currentStep < 2) {
      // Validate current step
      bool isValid = false;
      switch (_currentStep) {
        case 0:
          isValid = _nameController.text.trim().isNotEmpty &&
                   _designationController.text.trim().isNotEmpty;
          break;
        case 1:
          isValid = _selectedCompany != null;
          break;
      }

      if (isValid) {
        setState(() {
          _currentStep++;
        });
      } else {
        _showError('Please fill all required fields');
      }
    } else {
      // Final step - register employee
      _registerEmployee();
    }
  }
}
