import 'package:flutter/material.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:geolocator/geolocator.dart';
import '../../services/database_service.dart';
import '../../services/navigation_service.dart';
import '../../services/location_tracking_service.dart';
import '../../services/background_location_service.dart';
import '../../models/employee.dart';
import '../../models/company.dart';
import '../../models/attendance.dart';

class EmployeeDashboardScreen extends StatefulWidget {
  const EmployeeDashboardScreen({super.key});

  @override
  State<EmployeeDashboardScreen> createState() => _EmployeeDashboardScreenState();
}

class _EmployeeDashboardScreenState extends State<EmployeeDashboardScreen> {
  final DatabaseService _databaseService = DatabaseService();
  final FirebaseAuth _auth = FirebaseAuth.instance;
  final LocationTrackingService _locationService = LocationTrackingService();
  final BackgroundLocationService _backgroundService = BackgroundLocationService.instance;

  Employee? _employee;
  Company? _company;
  Attendance? _todayAttendance;
  bool _isLoading = true;
  bool _isCheckingIn = false;
  bool _isCheckingOut = false;
  bool _isLocationTrackingEnabled = false;
  Position? _currentPosition;
  double? _distanceFromOffice;

  @override
  void initState() {
    super.initState();
    _loadEmployeeData();
    _initializeLocationTracking();
  }

  @override
  void dispose() {
    _locationService.stopTracking();
    super.dispose();
  }

  Future<void> _loadEmployeeData() async {
    try {
      final user = _auth.currentUser;
      if (user != null) {
        debugPrint('🔍 Loading employee data for user: ${user.uid}');
        final employee = await _databaseService.getEmployeeById(user.uid);
        if (employee != null) {
          debugPrint('✅ Employee loaded: ${employee.name}, Company ID: ${employee.companyId}');

          // Load company data for location tracking
          final company = await _databaseService.getCompany(employee.companyId);

          if (company != null) {
            debugPrint('✅ Company loaded: ${company.name}');
          } else {
            debugPrint('❌ Company not found for ID: ${employee.companyId}');
            // Try to fix the employee's company ID
            await _fixEmployeeCompanyId(employee);
            // Reload data after fixing
            await _loadEmployeeData();
            return;
          }

          final todayAttendance = await _databaseService.getTodayAttendance(employee.id);
          setState(() {
            _employee = employee;
            _company = company;
            _todayAttendance = todayAttendance;
            _isLoading = false;
          });

          // Setup location tracking after data is loaded
          if (_company != null) {
            await _setupLocationTracking();
          } else {
            debugPrint('⚠️ Cannot setup location tracking - company data missing');
          }
        } else {
          debugPrint('❌ Employee not found for user: ${user.uid}');
          _showError('Employee data not found');
          NavigationService().navigateToWelcomeScreen();
        }
      } else {
        debugPrint('❌ No authenticated user found');
        NavigationService().navigateToWelcomeScreen();
      }
    } catch (e) {
      debugPrint('❌ Error loading employee data: $e');
      setState(() {
        _isLoading = false;
      });
      _showError('Failed to load employee data: $e');
    }
  }

  /// Initialize location tracking
  Future<void> _initializeLocationTracking() async {
    try {
      await _backgroundService.initialize();
      debugPrint('✅ Background location service initialized');
    } catch (e) {
      debugPrint('❌ Failed to initialize background location service: $e');
    }
  }

  /// Setup location tracking after employee and company data is loaded
  Future<void> _setupLocationTracking() async {
    if (_employee == null || _company == null) return;

    try {
      // Initialize location tracking service
      final success = await _locationService.initializeTracking(_employee!, _company!);

      if (success) {
        // Start real-time location tracking
        await _locationService.startTracking();

        // Start background location tracking
        await _backgroundService.startBackgroundTracking(_employee!.id, _company!.id);

        // Update distance from office
        await _updateDistanceFromOffice();

        setState(() {
          _isLocationTrackingEnabled = true;
        });

        debugPrint('✅ Location tracking enabled for ${_employee!.name}');
      } else {
        debugPrint('❌ Failed to setup location tracking');
      }
    } catch (e) {
      debugPrint('❌ Error setting up location tracking: $e');
    }
  }

  /// Update distance from office
  Future<void> _updateDistanceFromOffice() async {
    try {
      final distance = await _locationService.getDistanceFromOffice();
      if (distance != null) {
        setState(() {
          _distanceFromOffice = distance;
        });
      }
    } catch (e) {
      debugPrint('❌ Failed to update distance from office: $e');
    }
  }

  /// Fix employee's company ID if it's invalid
  Future<void> _fixEmployeeCompanyId(Employee employee) async {
    try {
      debugPrint('🔧 Attempting to fix employee company ID...');

      // Get all available companies
      final companies = await _databaseService.getAllCompanies();
      if (companies.isEmpty) {
        debugPrint('❌ No companies available to assign');
        return;
      }

      // Use the first available company
      final newCompany = companies.first;
      debugPrint('🔧 Assigning employee to company: ${newCompany.name} (${newCompany.id})');

      // Update employee with new company ID
      final updatedEmployee = Employee(
        id: employee.id,
        employeeId: employee.employeeId,
        name: employee.name,
        email: employee.email,
        designation: employee.designation,
        companyId: newCompany.id,
        companyName: newCompany.name,
        phoneNumber: employee.phoneNumber,
        isActive: employee.isActive,
        createdAt: employee.createdAt,
        updatedAt: DateTime.now(),
      );

      // Update in database using Firestore directly
      await FirebaseFirestore.instance
          .collection('employees')
          .doc(employee.id)
          .update({
        'companyId': newCompany.id,
        'companyName': newCompany.name,
        'updatedAt': DateTime.now(),
      });
      debugPrint('✅ Employee company ID fixed successfully');

    } catch (e) {
      debugPrint('❌ Failed to fix employee company ID: $e');
    }
  }

  /// Enable location tracking manually
  Future<void> _enableLocationTracking() async {
    debugPrint('🔍 Attempting to enable location tracking...');
    debugPrint('Employee data: ${_employee != null ? "✅ Available (${_employee!.name})" : "❌ Missing"}');
    debugPrint('Company data: ${_company != null ? "✅ Available (${_company!.name})" : "❌ Missing"}');

    if (_employee == null) {
      _showError('Employee data not found. Refreshing...');
      await _loadEmployeeData(); // Try to reload data
      if (_employee == null) {
        _showError('Unable to load employee data. Please restart the app.');
        return;
      }
    }

    if (_company == null) {
      _showError('Company data not found. Refreshing...');
      await _loadEmployeeData(); // Try to reload data
      if (_company == null) {
        _showError('Unable to load company data. Please contact your administrator.');
        return;
      }
    }

    try {
      debugPrint('🚀 Setting up location tracking for ${_employee!.name} at ${_company!.name}');
      await _setupLocationTracking();
      _showSuccess('Location tracking enabled successfully');
    } catch (e) {
      debugPrint('❌ Failed to enable location tracking: $e');
      _showError('Failed to enable location tracking: $e');
    }
  }

  Future<void> _getCurrentLocation() async {
    try {
      bool serviceEnabled = await Geolocator.isLocationServiceEnabled();
      if (!serviceEnabled) {
        _showError('Location services are disabled. Please enable them.');
        return;
      }

      LocationPermission permission = await Geolocator.checkPermission();
      if (permission == LocationPermission.denied) {
        permission = await Geolocator.requestPermission();
        if (permission == LocationPermission.denied) {
          _showError('Location permissions are denied');
          return;
        }
      }

      if (permission == LocationPermission.deniedForever) {
        _showError('Location permissions are permanently denied');
        return;
      }

      final position = await Geolocator.getCurrentPosition(
        locationSettings: const LocationSettings(
          accuracy: LocationAccuracy.high,
        ),
      );

      setState(() {
        _currentPosition = position;
      });
    } catch (e) {
      _showError('Failed to get location: $e');
    }
  }

  Future<void> _checkIn() async {
    if (_employee == null) return;

    setState(() {
      _isCheckingIn = true;
    });

    try {
      await _getCurrentLocation();

      if (_currentPosition == null) {
        _showError('Unable to get current location');
        return;
      }

      final attendance = Attendance(
        id: '', // Will be set by Firestore
        employeeId: _employee!.id,
        employeeName: _employee!.name,
        companyId: _employee!.companyId,
        checkInTime: DateTime.now(),
        checkInLatitude: _currentPosition!.latitude,
        checkInLongitude: _currentPosition!.longitude,
        date: DateTime.now(),
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
      );

      final success = await _databaseService.createAttendance(attendance);

      if (success) {
        _showSuccess('Checked in successfully!');
        _loadEmployeeData(); // Refresh data
      } else {
        _showError('Failed to check in. Please try again.');
      }
    } catch (e) {
      _showError('Check-in failed: $e');
    } finally {
      setState(() {
        _isCheckingIn = false;
      });
    }
  }

  Future<void> _checkOut() async {
    if (_employee == null || _todayAttendance == null) return;

    setState(() {
      _isCheckingOut = true;
    });

    try {
      await _getCurrentLocation();

      if (_currentPosition == null) {
        _showError('Unable to get current location');
        return;
      }

      final updatedAttendance = _todayAttendance!.copyWith(
        checkOutTime: DateTime.now(),
        checkOutLatitude: _currentPosition!.latitude,
        checkOutLongitude: _currentPosition!.longitude,
        updatedAt: DateTime.now(),
      );

      final success = await _databaseService.updateAttendance(updatedAttendance);

      if (success) {
        _showSuccess('Checked out successfully!');
        _loadEmployeeData(); // Refresh data
      } else {
        _showError('Failed to check out. Please try again.');
      }
    } catch (e) {
      _showError('Check-out failed: $e');
    } finally {
      setState(() {
        _isCheckingOut = false;
      });
    }
  }

  void _showError(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: Colors.red,
      ),
    );
  }

  void _showSuccess(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: Colors.green,
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    if (_isLoading) {
      return const Scaffold(
        body: Center(
          child: CircularProgressIndicator(
            valueColor: AlwaysStoppedAnimation<Color>(Color(0xFF667eea)),
          ),
        ),
      );
    }

    if (_employee == null) {
      return const Scaffold(
        body: Center(
          child: Text('Employee data not found'),
        ),
      );
    }

    return Scaffold(
      backgroundColor: Colors.grey[50],
      appBar: AppBar(
        title: const Text('Dashboard'),
        backgroundColor: const Color(0xFF667eea),
        foregroundColor: Colors.white,
        elevation: 0,
        actions: [
          PopupMenuButton<String>(
            onSelected: (value) {
              switch (value) {
                case 'profile':
                  _showProfileDialog();
                  break;
                case 'history':
                  NavigationService().navigateToAttendanceHistory();
                  break;
                case 'logout':
                  _logout();
                  break;
              }
            },
            itemBuilder: (context) => [
              const PopupMenuItem(
                value: 'profile',
                child: Row(
                  children: [
                    Icon(Icons.person, color: Colors.grey),
                    SizedBox(width: 12),
                    Text('Profile'),
                  ],
                ),
              ),
              const PopupMenuItem(
                value: 'history',
                child: Row(
                  children: [
                    Icon(Icons.history, color: Colors.grey),
                    SizedBox(width: 12),
                    Text('Attendance History'),
                  ],
                ),
              ),
              const PopupMenuItem(
                value: 'logout',
                child: Row(
                  children: [
                    Icon(Icons.logout, color: Colors.grey),
                    SizedBox(width: 12),
                    Text('Logout'),
                  ],
                ),
              ),
            ],
          ),
        ],
      ),
      body: RefreshIndicator(
        onRefresh: _loadEmployeeData,
        child: SingleChildScrollView(
          physics: const AlwaysScrollableScrollPhysics(),
          padding: const EdgeInsets.all(16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              _buildWelcomeCard(),
              const SizedBox(height: 24),
              _buildAttendanceCard(),
              const SizedBox(height: 24),
              _buildLocationStatus(),
              const SizedBox(height: 24),
              _buildQuickStats(),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildWelcomeCard() {
    return Container(
      width: double.infinity,
      padding: const EdgeInsets.all(24),
      decoration: BoxDecoration(
        gradient: const LinearGradient(
          colors: [Color(0xFF667eea), Color(0xFF764ba2)],
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
        ),
        borderRadius: BorderRadius.circular(20),
        boxShadow: [
          BoxShadow(
            color: const Color(0xFF667eea).withOpacity(0.3),
            blurRadius: 20,
            offset: const Offset(0, 10),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              CircleAvatar(
                radius: 30,
                backgroundColor: Colors.white.withOpacity(0.2),
                child: Text(
                  _employee!.name.isNotEmpty ? _employee!.name[0].toUpperCase() : 'E',
                  style: const TextStyle(
                    color: Colors.white,
                    fontSize: 24,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ),
              const SizedBox(width: 16),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    const Text(
                      'Welcome back!',
                      style: TextStyle(
                        color: Colors.white70,
                        fontSize: 16,
                      ),
                    ),
                    const SizedBox(height: 4),
                    Text(
                      _employee!.name,
                      style: const TextStyle(
                        color: Colors.white,
                        fontSize: 24,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    Text(
                      _employee!.designation,
                      style: const TextStyle(
                        color: Colors.white70,
                        fontSize: 14,
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
          const SizedBox(height: 20),
          Row(
            children: [
              const Icon(
                Icons.business,
                color: Colors.white70,
                size: 16,
              ),
              const SizedBox(width: 8),
              Text(
                _employee!.companyName,
                style: const TextStyle(
                  color: Colors.white70,
                  fontSize: 14,
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildAttendanceCard() {
    final isCheckedIn = _todayAttendance != null && _todayAttendance!.isCheckedIn;
    final hasCheckedOut = _todayAttendance != null && !_todayAttendance!.isCheckedIn;

    return Container(
      width: double.infinity,
      padding: const EdgeInsets.all(24),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(20),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.05),
            blurRadius: 10,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Container(
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  color: isCheckedIn ? Colors.green.withOpacity(0.1) : Colors.blue.withOpacity(0.1),
                  borderRadius: BorderRadius.circular(12),
                ),
                child: Icon(
                  isCheckedIn ? Icons.access_time : Icons.login,
                  color: isCheckedIn ? Colors.green : Colors.blue,
                  size: 24,
                ),
              ),
              const SizedBox(width: 16),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      isCheckedIn ? 'Checked In' : 'Ready to Check In',
                      style: const TextStyle(
                        fontSize: 18,
                        fontWeight: FontWeight.bold,
                        color: Colors.black87,
                      ),
                    ),
                    if (_todayAttendance != null) ...[
                      const SizedBox(height: 4),
                      Text(
                        'Check-in: ${_formatTime(_todayAttendance!.checkInTime)}',
                        style: TextStyle(
                          fontSize: 14,
                          color: Colors.grey[600],
                        ),
                      ),
                      if (hasCheckedOut)
                        Text(
                          'Check-out: ${_formatTime(_todayAttendance!.checkOutTime!)}',
                          style: TextStyle(
                            fontSize: 14,
                            color: Colors.grey[600],
                          ),
                        ),
                    ],
                  ],
                ),
              ),
            ],
          ),
          const SizedBox(height: 24),
          if (_todayAttendance == null)
            SizedBox(
              width: double.infinity,
              child: ElevatedButton(
                onPressed: _isCheckingIn ? null : _checkIn,
                style: ElevatedButton.styleFrom(
                  backgroundColor: Colors.green,
                  foregroundColor: Colors.white,
                  padding: const EdgeInsets.symmetric(vertical: 16),
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(12),
                  ),
                ),
                child: _isCheckingIn
                    ? const SizedBox(
                        width: 20,
                        height: 20,
                        child: CircularProgressIndicator(
                          valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                          strokeWidth: 2,
                        ),
                      )
                    : const Row(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          Icon(Icons.login),
                          SizedBox(width: 8),
                          Text('Check In'),
                        ],
                      ),
              ),
            )
          else if (isCheckedIn)
            SizedBox(
              width: double.infinity,
              child: ElevatedButton(
                onPressed: _isCheckingOut ? null : _checkOut,
                style: ElevatedButton.styleFrom(
                  backgroundColor: Colors.red,
                  foregroundColor: Colors.white,
                  padding: const EdgeInsets.symmetric(vertical: 16),
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(12),
                  ),
                ),
                child: _isCheckingOut
                    ? const SizedBox(
                        width: 20,
                        height: 20,
                        child: CircularProgressIndicator(
                          valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                          strokeWidth: 2,
                        ),
                      )
                    : const Row(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          Icon(Icons.logout),
                          SizedBox(width: 8),
                          Text('Check Out'),
                        ],
                      ),
              ),
            )
          else
            Container(
              width: double.infinity,
              padding: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                color: Colors.green.withOpacity(0.1),
                borderRadius: BorderRadius.circular(12),
              ),
              child: Column(
                children: [
                  const Icon(
                    Icons.check_circle,
                    color: Colors.green,
                    size: 32,
                  ),
                  const SizedBox(height: 8),
                  const Text(
                    'Work Complete!',
                    style: TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.w600,
                      color: Colors.green,
                    ),
                  ),
                  Text(
                    'Total time: ${_todayAttendance!.workDurationString}',
                    style: TextStyle(
                      fontSize: 14,
                      color: Colors.grey[600],
                    ),
                  ),
                ],
              ),
            ),
        ],
      ),
    );
  }

  Widget _buildLocationStatus() {
    return Container(
      width: double.infinity,
      padding: const EdgeInsets.all(24),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(20),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.05),
            blurRadius: 10,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(
                _isLocationTrackingEnabled ? Icons.location_on : Icons.location_off,
                color: _isLocationTrackingEnabled ? Colors.green : Colors.orange,
                size: 24,
              ),
              const SizedBox(width: 12),
              const Text(
                'Location Tracking',
                style: TextStyle(
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                  color: Colors.black87,
                ),
              ),
            ],
          ),
          const SizedBox(height: 16),

          // Location Status
          Container(
            width: double.infinity,
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: _locationService.isInOffice
                  ? Colors.green.withOpacity(0.1)
                  : Colors.orange.withOpacity(0.1),
              borderRadius: BorderRadius.circular(12),
            ),
            child: Column(
              children: [
                Icon(
                  _locationService.isInOffice ? Icons.business : Icons.location_city,
                  color: _locationService.isInOffice ? Colors.green : Colors.orange,
                  size: 32,
                ),
                const SizedBox(height: 8),
                Text(
                  _locationService.isInOffice ? 'In Office' : 'Outside Office',
                  style: TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.w600,
                    color: _locationService.isInOffice ? Colors.green : Colors.orange,
                  ),
                ),
                if (_distanceFromOffice != null) ...[
                  const SizedBox(height: 4),
                  Text(
                    'Distance: ${(_distanceFromOffice! / 1000).toStringAsFixed(2)} km',
                    style: const TextStyle(
                      fontSize: 14,
                      color: Colors.grey,
                    ),
                  ),
                ],
              ],
            ),
          ),

          const SizedBox(height: 16),

          // Location Controls
          Row(
            children: [
              Expanded(
                child: ElevatedButton.icon(
                  onPressed: _isLocationTrackingEnabled ? null : _enableLocationTracking,
                  icon: const Icon(Icons.my_location),
                  label: Text(_isLocationTrackingEnabled ? 'Tracking Active' : 'Enable Tracking'),
                  style: ElevatedButton.styleFrom(
                    backgroundColor: _isLocationTrackingEnabled ? Colors.green : Colors.blue,
                    foregroundColor: Colors.white,
                    padding: const EdgeInsets.symmetric(vertical: 12),
                  ),
                ),
              ),
              const SizedBox(width: 12),
              IconButton(
                onPressed: _updateDistanceFromOffice,
                icon: const Icon(Icons.refresh),
                tooltip: 'Refresh Location',
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildQuickStats() {
    return Container(
      padding: const EdgeInsets.all(24),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.05),
            blurRadius: 10,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Text(
            'Quick Actions',
            style: TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.bold,
              color: Colors.black87,
            ),
          ),
          const SizedBox(height: 16),
          Row(
            children: [
              Expanded(
                child: _buildQuickActionCard(
                  title: 'Attendance History',
                  icon: Icons.history,
                  color: Colors.blue,
                  onTap: () => NavigationService().navigateToAttendanceHistory(),
                ),
              ),
              const SizedBox(width: 16),
              Expanded(
                child: _buildQuickActionCard(
                  title: 'Profile',
                  icon: Icons.person,
                  color: Colors.purple,
                  onTap: _showProfileDialog,
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildQuickActionCard({
    required String title,
    required IconData icon,
    required Color color,
    required VoidCallback onTap,
  }) {
    return GestureDetector(
      onTap: onTap,
      child: Container(
        padding: const EdgeInsets.all(16),
        decoration: BoxDecoration(
          color: color.withOpacity(0.1),
          borderRadius: BorderRadius.circular(12),
        ),
        child: Column(
          children: [
            Icon(icon, color: color, size: 32),
            const SizedBox(height: 8),
            Text(
              title,
              style: TextStyle(
                fontSize: 14,
                fontWeight: FontWeight.w600,
                color: color,
              ),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      ),
    );
  }

  String _formatTime(DateTime time) {
    final hour = time.hour.toString().padLeft(2, '0');
    final minute = time.minute.toString().padLeft(2, '0');
    return '$hour:$minute';
  }

  void _showProfileDialog() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Profile'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            _buildProfileRow('Name', _employee!.name),
            _buildProfileRow('Email', _employee!.email),
            _buildProfileRow('Designation', _employee!.designation),
            _buildProfileRow('Company', _employee!.companyName),
            _buildProfileRow('Employee ID', _employee!.employeeId.isNotEmpty ? _employee!.employeeId : 'Not assigned'),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Close'),
          ),
        ],
      ),
    );
  }

  Widget _buildProfileRow(String label, String value) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 12),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            label,
            style: const TextStyle(
              fontSize: 14,
              fontWeight: FontWeight.w600,
              color: Colors.grey,
            ),
          ),
          const SizedBox(height: 4),
          Text(
            value,
            style: const TextStyle(
              fontSize: 16,
              color: Colors.black87,
            ),
          ),
        ],
      ),
    );
  }

  void _logout() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Logout'),
        content: const Text('Are you sure you want to logout?'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Cancel'),
          ),
          ElevatedButton(
            onPressed: () async {
              Navigator.of(context).pop();
              await _auth.signOut();
              NavigationService().navigateToWelcomeScreen();
            },
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.red,
              foregroundColor: Colors.white,
            ),
            child: const Text('Logout'),
          ),
        ],
      ),
    );
  }
}
