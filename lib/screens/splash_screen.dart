import 'package:flutter/material.dart';
import 'package:firebase_auth/firebase_auth.dart';
import '../services/navigation_service.dart';
import '../services/database_service.dart';

class SplashScreen extends StatefulWidget {
  const SplashScreen({super.key});

  @override
  State<SplashScreen> createState() => _SplashScreenState();
}

class _SplashScreenState extends State<SplashScreen>
    with SingleTickerProviderStateMixin {
  late AnimationController _animationController;
  late Animation<double> _fadeAnimation;
  late Animation<double> _scaleAnimation;

  @override
  void initState() {
    super.initState();
    
    _animationController = AnimationController(
      duration: const Duration(seconds: 2),
      vsync: this,
    );

    _fadeAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeIn,
    ));

    _scaleAnimation = Tween<double>(
      begin: 0.5,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.elasticOut,
    ));

    _startAnimation();
  }

  void _startAnimation() async {
    await _animationController.forward();

    // Wait for animation and a bit more for better UX
    await Future.delayed(const Duration(milliseconds: 1500));

    // Navigate to welcome screen
    _navigateToNextScreen();
  }

  void _navigateToNextScreen() async {
    final navigationService = NavigationService();

    try {
      // Check if user is already logged in
      final currentUser = FirebaseAuth.instance.currentUser;

      if (currentUser != null) {
        debugPrint('🔄 User already logged in: ${currentUser.email}');

        // Check if user is an employee by looking up in database
        final databaseService = DatabaseService();
        final employee = await databaseService.getEmployeeById(currentUser.uid);

        if (employee != null) {
          debugPrint('✅ Auto-login: Navigating to Employee Dashboard for ${employee.name}');
          navigationService.navigateToEmployeeDashboard();
          return;
        } else {
          debugPrint('❌ User authenticated but no employee record found');
          // Sign out the user since they don't have a valid employee record
          await FirebaseAuth.instance.signOut();
        }
      } else {
        debugPrint('ℹ️ No user logged in, showing welcome screen');
      }

      // If no valid logged-in user, go to welcome screen
      navigationService.navigateToWelcomeScreen();

    } catch (e) {
      debugPrint('❌ Error checking authentication status: $e');
      // On error, go to welcome screen
      navigationService.navigateToWelcomeScreen();
    }
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: const Color(0xFF667eea),
      body: Center(
        child: AnimatedBuilder(
          animation: _animationController,
          builder: (context, child) {
            return FadeTransition(
              opacity: _fadeAnimation,
              child: ScaleTransition(
                scale: _scaleAnimation,
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    // App Icon
                    Container(
                      width: 120,
                      height: 120,
                      decoration: BoxDecoration(
                        color: Colors.white,
                        borderRadius: BorderRadius.circular(30),
                        boxShadow: [
                          BoxShadow(
                            color: Colors.black.withValues(alpha: 0.2),
                            blurRadius: 20,
                            offset: const Offset(0, 10),
                          ),
                        ],
                      ),
                      child: const Icon(
                        Icons.access_time_rounded,
                        size: 60,
                        color: Color(0xFF667eea),
                      ),
                    ),
                    const SizedBox(height: 30),
                    
                    // App Name
                    const Text(
                      'timeSync',
                      style: TextStyle(
                        fontSize: 32,
                        fontWeight: FontWeight.bold,
                        color: Colors.white,
                        letterSpacing: 1.2,
                      ),
                    ),
                    const SizedBox(height: 10),
                    
                    // Tagline
                    const Text(
                      'Employee Attendance Made Simple',
                      style: TextStyle(
                        fontSize: 16,
                        color: Colors.white70,
                        fontWeight: FontWeight.w300,
                      ),
                    ),
                    const SizedBox(height: 50),
                    
                    // Loading indicator
                    const SizedBox(
                      width: 30,
                      height: 30,
                      child: CircularProgressIndicator(
                        valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                        strokeWidth: 2,
                      ),
                    ),
                  ],
                ),
              ),
            );
          },
        ),
      ),
    );
  }
}
