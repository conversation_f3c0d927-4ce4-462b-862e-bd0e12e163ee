import 'package:flutter/material.dart';
import '../../services/navigation_service.dart';
import '../../services/database_service.dart';
import '../../services/super_admin_service.dart';

class AccessCodeInputScreen extends StatefulWidget {
  const AccessCodeInputScreen({super.key});

  @override
  State<AccessCodeInputScreen> createState() => _AccessCodeInputScreenState();
}

class _AccessCodeInputScreenState extends State<AccessCodeInputScreen> {
  final TextEditingController _codeController = TextEditingController();
  final FocusNode _codeFocusNode = FocusNode();
  bool _isLoading = false;
  String? _errorMessage;

  @override
  void initState() {
    super.initState();
    // Auto-focus the text field
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _codeFocusNode.requestFocus();
    });
  }

  @override
  void dispose() {
    _codeController.dispose();
    _codeFocusNode.dispose();
    super.dispose();
  }

  Future<void> _verifyCode() async {
    final code = _codeController.text.trim(); // Keep original case

    debugPrint('🔍 ACCESS CODE: Starting verification for code: "$code"');

    if (code.isEmpty) {
      debugPrint('❌ ACCESS CODE: Empty code provided');
      setState(() {
        _errorMessage = 'Please enter an access code';
      });
      return;
    }

    setState(() {
      _isLoading = true;
      _errorMessage = null;
    });

    try {
      debugPrint('🔍 ACCESS CODE: Checking if Super Admin code...');
      // First check if it's a super admin code
      final superAdminService = SuperAdminService();
      final isSuperAdmin = await superAdminService.verifyAccessCode(code);
      debugPrint('🔍 ACCESS CODE: Super Admin check result: $isSuperAdmin');

      if (isSuperAdmin) {
        debugPrint('✅ ACCESS CODE: Super Admin access granted');
        // Super Admin access - navigate directly
        NavigationService().navigateToSuperAdminDashboard();
        return;
      }

      debugPrint('🔍 ACCESS CODE: Checking if Company Owner code...');
      // Check if it's a company owner code
      final databaseService = DatabaseService();
      debugPrint('🔍 ACCESS CODE: Searching for company with owner code: $code');
      final company = await databaseService.getCompanyByOwnerCode(code);
      debugPrint('🔍 ACCESS CODE: Company lookup result: ${company?.name ?? 'null'}');

      if (company != null) {
        debugPrint('✅ ACCESS CODE: Company Owner access granted for: ${company.name}');
        // Company Owner access - navigate directly
        NavigationService().navigateToCompanyOwnerDashboard(
          company: company,
          accessCode: code,
        );
        return;
      }

      debugPrint('❌ ACCESS CODE: No valid access found for code: $code');
      // Invalid code
      setState(() {
        _errorMessage = 'Invalid access code. Please try again.';
      });

    } catch (e) {
      debugPrint('❌ ACCESS CODE: Exception during verification: $e');
      setState(() {
        _errorMessage = 'Error verifying code. Please try again.';
      });
    } finally {
      debugPrint('🔍 ACCESS CODE: Verification completed, setting loading to false');
      setState(() {
        _isLoading = false;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.white,
      appBar: AppBar(
        backgroundColor: Colors.transparent,
        elevation: 0,
        leading: IconButton(
          icon: const Icon(Icons.arrow_back, color: Colors.black87),
          onPressed: () => NavigationService().goBack(),
        ),
      ),
      body: SafeArea(
        child: Padding(
          padding: const EdgeInsets.all(24.0),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              const SizedBox(height: 20),
              
              // Title
              const Text(
                'Enter Access Code',
                style: TextStyle(
                  fontSize: 32,
                  fontWeight: FontWeight.bold,
                  color: Colors.black87,
                ),
              ),
              const SizedBox(height: 12),
              
              // Subtitle
              const Text(
                'Enter your company access code or super admin code to continue.',
                style: TextStyle(
                  fontSize: 16,
                  color: Colors.grey,
                  height: 1.5,
                ),
              ),
              const SizedBox(height: 40),
              
              // Code Input Field
              TextField(
                controller: _codeController,
                focusNode: _codeFocusNode,
                // Remove auto-capitalization to preserve case sensitivity
                style: const TextStyle(
                  fontSize: 18,
                  fontWeight: FontWeight.w600,
                  letterSpacing: 2,
                ),
                decoration: InputDecoration(
                  labelText: 'Access Code',
                  hintText: 'Enter your code',
                  errorText: _errorMessage,
                  border: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(16),
                    borderSide: const BorderSide(color: Colors.grey),
                  ),
                  focusedBorder: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(16),
                    borderSide: const BorderSide(color: Color(0xFF667eea), width: 2),
                  ),
                  errorBorder: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(16),
                    borderSide: const BorderSide(color: Colors.red, width: 2),
                  ),
                  contentPadding: const EdgeInsets.symmetric(
                    horizontal: 20,
                    vertical: 16,
                  ),
                ),
                onChanged: (value) {
                  debugPrint('🔍 INPUT: Text changed to: "$value"');
                },
                onSubmitted: (value) {
                  debugPrint('🔍 INPUT: Form submitted with value: "$value"');
                  _verifyCode();
                },
              ),
              const SizedBox(height: 32),
              
              // Verify Button
              SizedBox(
                width: double.infinity,
                height: 56,
                child: ElevatedButton(
                  onPressed: _isLoading ? null : () {
                    debugPrint('🔍 BUTTON: Verify Code button pressed!');
                    _verifyCode();
                  },
                  style: ElevatedButton.styleFrom(
                    backgroundColor: const Color(0xFF667eea),
                    foregroundColor: Colors.white,
                    elevation: 0,
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(16),
                    ),
                  ),
                  child: _isLoading
                      ? const SizedBox(
                          width: 24,
                          height: 24,
                          child: CircularProgressIndicator(
                            valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                            strokeWidth: 2,
                          ),
                        )
                      : const Text(
                          'Verify Code',
                          style: TextStyle(
                            fontSize: 18,
                            fontWeight: FontWeight.w600,
                          ),
                        ),
                ),
              ),
              const SizedBox(height: 24),
              
              // Help Text
              Container(
                padding: const EdgeInsets.all(16),
                decoration: BoxDecoration(
                  color: Colors.blue.withOpacity(0.1),
                  borderRadius: BorderRadius.circular(12),
                ),
                child: Row(
                  children: [
                    Icon(
                      Icons.info_outline,
                      color: Colors.blue.shade700,
                      size: 20,
                    ),
                    const SizedBox(width: 12),
                    Expanded(
                      child: Text(
                        'Contact your company administrator to get your access code.',
                        style: TextStyle(
                          fontSize: 14,
                          color: Colors.blue.shade700,
                        ),
                      ),
                    ),
                  ],
                ),
              ),
              
              const Spacer(),
              
              // Employee Registration Link
              Center(
                child: TextButton(
                  onPressed: () {
                    NavigationService().navigateToEmployeeOnboarding();
                  },
                  child: const Text(
                    'New employee? Register here',
                    style: TextStyle(
                      fontSize: 16,
                      color: Color(0xFF667eea),
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}
