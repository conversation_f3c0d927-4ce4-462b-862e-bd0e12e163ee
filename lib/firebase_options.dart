// File generated by FlutterFire CLI.
// ignore_for_file: type=lint
import 'package:firebase_core/firebase_core.dart' show FirebaseOptions;
import 'package:flutter/foundation.dart'
    show defaultTargetPlatform, kIsWeb, TargetPlatform;

/// Default [FirebaseOptions] for use with your Firebase apps.
///
/// Example:
/// ```dart
/// import 'firebase_options.dart';
/// // ...
/// await Firebase.initializeApp(
///   options: DefaultFirebaseOptions.currentPlatform,
/// );
/// ```
class DefaultFirebaseOptions {
  static FirebaseOptions get currentPlatform {
    if (kIsWeb) {
      return web;
    }
    switch (defaultTargetPlatform) {
      case TargetPlatform.android:
        return android;
      case TargetPlatform.iOS:
        return ios;
      case TargetPlatform.macOS:
        return macos;
      case TargetPlatform.windows:
        return windows;
      case TargetPlatform.linux:
        throw UnsupportedError(
          'DefaultFirebaseOptions have not been configured for linux - '
          'you can reconfigure this by running the FlutterFire CLI again.',
        );
      default:
        throw UnsupportedError(
          'DefaultFirebaseOptions are not supported for this platform.',
        );
    }
  }

  static const FirebaseOptions web = FirebaseOptions(
    apiKey: 'AIzaSyCp5HE2Pogiuk_jyHlGRBx5TKDvBnpoOrg',
    appId: '1:766651579888:web:7974aff96c4e1b2428f135',
    messagingSenderId: '766651579888',
    projectId: 'timesync-2e7a1',
    authDomain: 'timesync-2e7a1.firebaseapp.com',
    storageBucket: 'timesync-2e7a1.firebasestorage.app',
    measurementId: 'G-RSVEPRK0LH',
  );

  static const FirebaseOptions android = FirebaseOptions(
    apiKey: 'AIzaSyDP9r2lAHkF8PWQu-YChcYcYbeqwJnf7_E',
    appId: '1:766651579888:android:663eb18eeae5ce2328f135',
    messagingSenderId: '766651579888',
    projectId: 'timesync-2e7a1',
    storageBucket: 'timesync-2e7a1.firebasestorage.app',
  );

  static const FirebaseOptions ios = FirebaseOptions(
    apiKey: 'AIzaSyBss0mRu_ev6Tsu20s2a5sxijM7z4y5uS8',
    appId: '1:766651579888:ios:dd62f8bee473d55b28f135',
    messagingSenderId: '766651579888',
    projectId: 'timesync-2e7a1',
    storageBucket: 'timesync-2e7a1.firebasestorage.app',
    iosBundleId: 'timesync.in',
  );

  static const FirebaseOptions macos = FirebaseOptions(
    apiKey: 'your-macos-api-key',
    appId: 'your-macos-app-id',
    messagingSenderId: 'your-sender-id',
    projectId: 'your-project-id',
    storageBucket: 'your-project-id.appspot.com',
    iosBundleId: 'timesync.in',
  );

  static const FirebaseOptions windows = FirebaseOptions(
    apiKey: 'your-windows-api-key',
    appId: 'your-windows-app-id',
    messagingSenderId: 'your-sender-id',
    projectId: 'your-project-id',
    authDomain: 'your-project-id.firebaseapp.com',
    storageBucket: 'your-project-id.appspot.com',
    measurementId: 'your-measurement-id',
  );
}