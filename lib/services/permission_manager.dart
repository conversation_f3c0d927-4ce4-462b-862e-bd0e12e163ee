import 'dart:io';
import 'package:flutter/material.dart';
import 'package:permission_handler/permission_handler.dart';
import 'package:geolocator/geolocator.dart';
import 'package:flutter_local_notifications/flutter_local_notifications.dart';

class PermissionManager {
  static final PermissionManager _instance = PermissionManager._internal();
  factory PermissionManager() => _instance;
  PermissionManager._internal();

  final FlutterLocalNotificationsPlugin _notificationsPlugin =
      FlutterLocalNotificationsPlugin();

  /// Initialize notification plugin
  Future<void> initializeNotifications() async {
    const AndroidInitializationSettings initializationSettingsAndroid =
        AndroidInitializationSettings('@mipmap/ic_launcher');

    const DarwinInitializationSettings initializationSettingsIOS =
        DarwinInitializationSettings(
      requestAlertPermission: true,
      requestBadgePermission: true,
      requestSoundPermission: true,
    );

    const InitializationSettings initializationSettings =
        InitializationSettings(
      android: initializationSettingsAndroid,
      iOS: initializationSettingsIOS,
    );

    await _notificationsPlugin.initialize(initializationSettings);
  }

  /// Check if all required permissions are granted
  Future<bool> areAllPermissionsGranted() async {
    final locationStatus = await _getLocationPermissionStatus();
    final notificationStatus = await _getNotificationPermissionStatus();
    final internetStatus = true; // Internet permission is automatically granted

    return locationStatus && notificationStatus && internetStatus;
  }

  /// Request all required permissions with persistent retry logic
  Future<bool> requestAllPermissions(BuildContext context) async {
    bool allGranted = false;
    int attempts = 0;
    const maxAttempts = 10; // Maximum retry attempts

    while (!allGranted && attempts < maxAttempts) {
      attempts++;
      
      // Request location permissions
      final locationGranted = await _requestLocationPermissions(context);
      
      // Request notification permissions
      final notificationGranted = await _requestNotificationPermissions(context);
      
      allGranted = locationGranted && notificationGranted;
      
      if (!allGranted) {
        // Show dialog explaining why permissions are needed
        final shouldRetry = await _showPermissionDialog(context, attempts);
        if (!shouldRetry) {
          break;
        }
        
        // Wait a bit before retrying
        await Future.delayed(const Duration(seconds: 1));
      }
    }

    return allGranted;
  }

  /// Get location permission status
  Future<bool> _getLocationPermissionStatus() async {
    if (Platform.isAndroid) {
      final fineLocation = await Permission.location.status;
      final backgroundLocation = await Permission.locationAlways.status;
      return fineLocation.isGranted && backgroundLocation.isGranted;
    } else if (Platform.isIOS) {
      final locationStatus = await Geolocator.checkPermission();
      return locationStatus == LocationPermission.always ||
             locationStatus == LocationPermission.whileInUse;
    }
    return false;
  }

  /// Get notification permission status
  Future<bool> _getNotificationPermissionStatus() async {
    if (Platform.isAndroid) {
      final status = await Permission.notification.status;
      return status.isGranted;
    } else if (Platform.isIOS) {
      final settings = await _notificationsPlugin
          .resolvePlatformSpecificImplementation<
              IOSFlutterLocalNotificationsPlugin>()
          ?.requestPermissions(
            alert: true,
            badge: true,
            sound: true,
          );
      return settings ?? false;
    }
    return false;
  }

  /// Request location permissions
  Future<bool> _requestLocationPermissions(BuildContext context) async {
    try {
      if (Platform.isAndroid) {
        // Request fine location first
        final fineLocationStatus = await Permission.location.request();
        
        if (fineLocationStatus.isGranted) {
          // Then request background location
          final backgroundLocationStatus = await Permission.locationAlways.request();
          
          if (backgroundLocationStatus.isDenied || backgroundLocationStatus.isPermanentlyDenied) {
            await _showLocationSettingsDialog(context);
            return false;
          }
          
          return backgroundLocationStatus.isGranted;
        } else if (fineLocationStatus.isPermanentlyDenied) {
          await _showLocationSettingsDialog(context);
          return false;
        }
        
        return false;
      } else if (Platform.isIOS) {
        LocationPermission permission = await Geolocator.checkPermission();
        
        if (permission == LocationPermission.denied) {
          permission = await Geolocator.requestPermission();
        }
        
        if (permission == LocationPermission.deniedForever) {
          await _showLocationSettingsDialog(context);
          return false;
        }
        
        return permission == LocationPermission.always ||
               permission == LocationPermission.whileInUse;
      }
    } catch (e) {
      debugPrint('Error requesting location permissions: $e');
    }
    
    return false;
  }

  /// Request notification permissions
  Future<bool> _requestNotificationPermissions(BuildContext context) async {
    try {
      if (Platform.isAndroid) {
        final status = await Permission.notification.request();
        
        if (status.isPermanentlyDenied) {
          await _showNotificationSettingsDialog(context);
          return false;
        }
        
        return status.isGranted;
      } else if (Platform.isIOS) {
        final granted = await _notificationsPlugin
            .resolvePlatformSpecificImplementation<
                IOSFlutterLocalNotificationsPlugin>()
            ?.requestPermissions(
              alert: true,
              badge: true,
              sound: true,
            );
        
        if (granted == false) {
          await _showNotificationSettingsDialog(context);
        }
        
        return granted ?? false;
      }
    } catch (e) {
      debugPrint('Error requesting notification permissions: $e');
    }
    
    return false;
  }

  /// Show permission explanation dialog
  Future<bool> _showPermissionDialog(BuildContext context, int attempt) async {
    return await showDialog<bool>(
      context: context,
      barrierDismissible: false,
      builder: (BuildContext context) {
        return AlertDialog(
          title: const Text('Permissions Required'),
          content: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              const Icon(
                Icons.security,
                size: 48,
                color: Color(0xFF667eea),
              ),
              const SizedBox(height: 16),
              Text(
                'timeSync requires the following permissions to function properly:\n\n'
                '• Location: For accurate time synchronization\n'
                '• Background Location: To sync time even when app is closed\n'
                '• Notifications: To alert you about sync events\n'
                '• Internet: To connect to time servers\n\n'
                'Attempt $attempt of 10',
                textAlign: TextAlign.left,
              ),
            ],
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(context).pop(false),
              child: const Text('Exit App'),
            ),
            ElevatedButton(
              onPressed: () => Navigator.of(context).pop(true),
              style: ElevatedButton.styleFrom(
                backgroundColor: const Color(0xFF667eea),
                foregroundColor: Colors.white,
              ),
              child: const Text('Grant Permissions'),
            ),
          ],
        );
      },
    ) ?? false;
  }

  /// Show location settings dialog
  Future<void> _showLocationSettingsDialog(BuildContext context) async {
    await showDialog(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          title: const Text('Location Permission Required'),
          content: const Text(
            'timeSync needs location access to provide accurate time synchronization. '
            'Please enable location permissions in your device settings.',
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(context).pop(),
              child: const Text('Cancel'),
            ),
            ElevatedButton(
              onPressed: () {
                Navigator.of(context).pop();
                openAppSettings();
              },
              style: ElevatedButton.styleFrom(
                backgroundColor: const Color(0xFF667eea),
                foregroundColor: Colors.white,
              ),
              child: const Text('Open Settings'),
            ),
          ],
        );
      },
    );
  }

  /// Show notification settings dialog
  Future<void> _showNotificationSettingsDialog(BuildContext context) async {
    await showDialog(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          title: const Text('Notification Permission Required'),
          content: const Text(
            'timeSync needs notification access to alert you about time synchronization events. '
            'Please enable notification permissions in your device settings.',
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(context).pop(),
              child: const Text('Cancel'),
            ),
            ElevatedButton(
              onPressed: () {
                Navigator.of(context).pop();
                openAppSettings();
              },
              style: ElevatedButton.styleFrom(
                backgroundColor: const Color(0xFF667eea),
                foregroundColor: Colors.white,
              ),
              child: const Text('Open Settings'),
            ),
          ],
        );
      },
    );
  }

  /// Request battery optimization exemption (Android)
  Future<void> requestBatteryOptimizationExemption() async {
    if (Platform.isAndroid) {
      final status = await Permission.ignoreBatteryOptimizations.status;
      if (!status.isGranted) {
        await Permission.ignoreBatteryOptimizations.request();
      }
    }
  }
}
