import 'package:flutter/material.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'firebase_service.dart';
import 'auth_service.dart';

class DataService {
  static final DataService _instance = DataService._internal();
  factory DataService() => _instance;
  DataService._internal();

  final FirebaseService _firebaseService = FirebaseService();
  final AuthService _authService = AuthService();

  /// Get Firestore instance
  FirebaseFirestore get firestore => _firebaseService.firestore;

  /// Get current user ID
  String? get currentUserId => _authService.currentUser?.uid;

  /// Save time sync data
  Future<bool> saveTimeSyncData(Map<String, dynamic> data) async {
    try {
      if (currentUserId == null) {
        debugPrint('No user signed in');
        return false;
      }

      await firestore
          .collection('users')
          .doc(currentUserId)
          .collection('timeSyncData')
          .add({
        ...data,
        'timestamp': FieldValue.serverTimestamp(),
        'userId': currentUserId,
      });

      await _firebaseService.logEvent('time_sync_data_saved');
      return true;
    } catch (e) {
      await _firebaseService.logError(e, StackTrace.current, reason: 'Failed to save time sync data');
      debugPrint('Failed to save time sync data: $e');
      return false;
    }
  }

  /// Get time sync data stream
  Stream<QuerySnapshot> getTimeSyncDataStream() {
    if (currentUserId == null) {
      return const Stream.empty();
    }

    return firestore
        .collection('users')
        .doc(currentUserId)
        .collection('timeSyncData')
        .orderBy('timestamp', descending: true)
        .limit(100)
        .snapshots();
  }

  /// Save user preferences
  Future<bool> saveUserPreferences(Map<String, dynamic> preferences) async {
    try {
      if (currentUserId == null) {
        debugPrint('No user signed in');
        return false;
      }

      await firestore
          .collection('users')
          .doc(currentUserId)
          .update({
        'preferences': preferences,
        'updatedAt': FieldValue.serverTimestamp(),
      });

      await _firebaseService.logEvent('user_preferences_saved');
      return true;
    } catch (e) {
      await _firebaseService.logError(e, StackTrace.current, reason: 'Failed to save user preferences');
      debugPrint('Failed to save user preferences: $e');
      return false;
    }
  }

  /// Get user preferences
  Future<Map<String, dynamic>?> getUserPreferences() async {
    try {
      if (currentUserId == null) {
        debugPrint('No user signed in');
        return null;
      }

      final doc = await firestore
          .collection('users')
          .doc(currentUserId)
          .get();

      if (doc.exists && doc.data() != null) {
        return doc.data()!['preferences'] as Map<String, dynamic>?;
      }
      return null;
    } catch (e) {
      await _firebaseService.logError(e, StackTrace.current, reason: 'Failed to get user preferences');
      debugPrint('Failed to get user preferences: $e');
      return null;
    }
  }

  /// Save location data
  Future<bool> saveLocationData(double latitude, double longitude, {String? address}) async {
    try {
      if (currentUserId == null) {
        debugPrint('No user signed in');
        return false;
      }

      await firestore
          .collection('users')
          .doc(currentUserId)
          .collection('locations')
          .add({
        'latitude': latitude,
        'longitude': longitude,
        'address': address,
        'timestamp': FieldValue.serverTimestamp(),
        'userId': currentUserId,
      });

      await _firebaseService.logEvent('location_data_saved');
      return true;
    } catch (e) {
      await _firebaseService.logError(e, StackTrace.current, reason: 'Failed to save location data');
      debugPrint('Failed to save location data: $e');
      return false;
    }
  }

  /// Get recent locations
  Future<List<Map<String, dynamic>>> getRecentLocations({int limit = 10}) async {
    try {
      if (currentUserId == null) {
        debugPrint('No user signed in');
        return [];
      }

      final querySnapshot = await firestore
          .collection('users')
          .doc(currentUserId)
          .collection('locations')
          .orderBy('timestamp', descending: true)
          .limit(limit)
          .get();

      return querySnapshot.docs
          .map((doc) => {'id': doc.id, ...doc.data()})
          .toList();
    } catch (e) {
      await _firebaseService.logError(e, StackTrace.current, reason: 'Failed to get recent locations');
      debugPrint('Failed to get recent locations: $e');
      return [];
    }
  }

  /// Save app usage analytics
  Future<bool> saveAppUsageData(String action, {Map<String, dynamic>? metadata}) async {
    try {
      if (currentUserId == null) {
        debugPrint('No user signed in');
        return false;
      }

      await firestore
          .collection('analytics')
          .add({
        'userId': currentUserId,
        'action': action,
        'metadata': metadata ?? {},
        'timestamp': FieldValue.serverTimestamp(),
        'platform': Theme.of(NavigationService.navigatorKey.currentContext!).platform.name,
        'appVersion': '1.0.0',
      });

      await _firebaseService.logEvent('app_usage_tracked', parameters: {
        'action': action,
        if (metadata != null) ...metadata,
      });
      
      return true;
    } catch (e) {
      await _firebaseService.logError(e, StackTrace.current, reason: 'Failed to save app usage data');
      debugPrint('Failed to save app usage data: $e');
      return false;
    }
  }

  /// Get app configuration from Firestore
  Future<Map<String, dynamic>?> getAppConfiguration() async {
    try {
      final doc = await firestore
          .collection('config')
          .doc('app')
          .get();

      if (doc.exists && doc.data() != null) {
        return doc.data()!;
      }
      return null;
    } catch (e) {
      await _firebaseService.logError(e, StackTrace.current, reason: 'Failed to get app configuration');
      debugPrint('Failed to get app configuration: $e');
      return null;
    }
  }

  /// Save device info
  Future<bool> saveDeviceInfo(Map<String, dynamic> deviceInfo) async {
    try {
      if (currentUserId == null) {
        debugPrint('No user signed in');
        return false;
      }

      await firestore
          .collection('users')
          .doc(currentUserId)
          .update({
        'deviceInfo': deviceInfo,
        'lastDeviceUpdate': FieldValue.serverTimestamp(),
      });

      await _firebaseService.logEvent('device_info_saved');
      return true;
    } catch (e) {
      await _firebaseService.logError(e, StackTrace.current, reason: 'Failed to save device info');
      debugPrint('Failed to save device info: $e');
      return false;
    }
  }

  /// Batch write operations
  Future<bool> batchWrite(List<Map<String, dynamic>> operations) async {
    try {
      if (currentUserId == null) {
        debugPrint('No user signed in');
        return false;
      }

      final batch = firestore.batch();

      for (final operation in operations) {
        final collection = operation['collection'] as String;
        final data = operation['data'] as Map<String, dynamic>;
        final docId = operation['docId'] as String?;

        DocumentReference docRef;
        if (docId != null) {
          docRef = firestore.collection(collection).doc(docId);
        } else {
          docRef = firestore.collection(collection).doc();
        }

        batch.set(docRef, {
          ...data,
          'timestamp': FieldValue.serverTimestamp(),
          'userId': currentUserId,
        });
      }

      await batch.commit();
      await _firebaseService.logEvent('batch_write_completed', parameters: {
        'operations_count': operations.length,
      });
      
      return true;
    } catch (e) {
      await _firebaseService.logError(e, StackTrace.current, reason: 'Failed to execute batch write');
      debugPrint('Failed to execute batch write: $e');
      return false;
    }
  }

  /// Delete user data
  Future<bool> deleteUserData() async {
    try {
      if (currentUserId == null) {
        debugPrint('No user signed in');
        return false;
      }

      final batch = firestore.batch();

      // Delete user's time sync data
      final timeSyncQuery = await firestore
          .collection('users')
          .doc(currentUserId)
          .collection('timeSyncData')
          .get();

      for (final doc in timeSyncQuery.docs) {
        batch.delete(doc.reference);
      }

      // Delete user's location data
      final locationQuery = await firestore
          .collection('users')
          .doc(currentUserId)
          .collection('locations')
          .get();

      for (final doc in locationQuery.docs) {
        batch.delete(doc.reference);
      }

      // Delete user document
      batch.delete(firestore.collection('users').doc(currentUserId!));

      await batch.commit();
      await _firebaseService.logEvent('user_data_deleted');
      
      return true;
    } catch (e) {
      await _firebaseService.logError(e, StackTrace.current, reason: 'Failed to delete user data');
      debugPrint('Failed to delete user data: $e');
      return false;
    }
  }
}

/// Navigation service for global context access
class NavigationService {
  static final GlobalKey<NavigatorState> navigatorKey = GlobalKey<NavigatorState>();
}
