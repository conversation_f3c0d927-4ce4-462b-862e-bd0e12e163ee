import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:flutter/foundation.dart';
import '../models/company.dart';
import '../models/employee.dart';
import '../models/attendance_record.dart';
import '../models/attendance.dart';
import '../models/daily_attendance.dart';
import '../models/super_admin.dart';
import 'firebase_service.dart';
import 'code_generator_service.dart';

class DatabaseService {
  static final DatabaseService _instance = DatabaseService._internal();
  factory DatabaseService() => _instance;
  DatabaseService._internal();

  final FirebaseService _firebaseService = FirebaseService();
  final CodeGeneratorService _codeGenerator = CodeGeneratorService();
  FirebaseFirestore get _firestore => _firebaseService.firestore;

  // Collection references
  static const String companiesCollection = 'companies';
  static const String employeesCollection = 'employees';
  static const String attendanceRecordsCollection = 'attendance_records';
  static const String dailyAttendanceCollection = 'daily_attendance';
  static const String superAdminCollection = 'super_admin';

  // ==================== SUPER ADMIN OPERATIONS ====================

  /// Get Super Admin access code
  Future<SuperAdmin?> getSuperAdmin() async {
    try {
      final querySnapshot = await _firestore
          .collection(superAdminCollection)
          .where('isActive', isEqualTo: true)
          .limit(1)
          .get();

      if (querySnapshot.docs.isNotEmpty) {
        return SuperAdmin.fromSnapshot(querySnapshot.docs.first);
      }
      return null;
    } catch (e) {
      debugPrint('Error getting super admin: $e');
      return null;
    }
  }

  /// Create or update Super Admin
  Future<bool> createOrUpdateSuperAdmin(String accessCode) async {
    try {
      final existingSuperAdmin = await getSuperAdmin();
      
      if (existingSuperAdmin != null) {
        // Update existing
        await _firestore
            .collection(superAdminCollection)
            .doc(existingSuperAdmin.id)
            .update({
          'accessCode': accessCode,
          'updatedAt': FieldValue.serverTimestamp(),
        });
      } else {
        // Create new
        await _firestore.collection(superAdminCollection).add({
          'accessCode': accessCode,
          'isActive': true,
          'createdAt': FieldValue.serverTimestamp(),
          'updatedAt': FieldValue.serverTimestamp(),
        });
      }
      return true;
    } catch (e) {
      debugPrint('Error creating/updating super admin: $e');
      return false;
    }
  }

  /// Verify Super Admin access code
  Future<bool> verifySuperAdminCode(String code) async {
    try {
      final superAdmin = await getSuperAdmin();
      return superAdmin?.accessCode == code;
    } catch (e) {
      debugPrint('Error verifying super admin code: $e');
      return false;
    }
  }

  // ==================== COMPANY OPERATIONS ====================

  /// Create a new company
  Future<String?> createCompany(Company company) async {
    try {
      final docRef = await _firestore.collection(companiesCollection).add(company.toMap());
      return docRef.id;
    } catch (e) {
      debugPrint('Error creating company: $e');
      return null;
    }
  }

  /// Get company by owner code
  Future<Company?> getCompanyByOwnerCode(String ownerCode) async {
    try {
      debugPrint('🔍 DATABASE: Searching for company with owner code: $ownerCode');

      // Try to get from cache first (offline support)
      try {
        final cacheSnapshot = await _firestore
            .collection(companiesCollection)
            .where('ownerCode', isEqualTo: ownerCode)
            .limit(1)
            .get(const GetOptions(source: Source.cache));

        if (cacheSnapshot.docs.isNotEmpty) {
          final company = Company.fromSnapshot(cacheSnapshot.docs.first);
          debugPrint('✅ DATABASE: Found company in cache: ${company.name} (${company.ownerCode})');
          return company;
        }
        debugPrint('🔍 DATABASE: Not found in cache, trying server...');
      } catch (cacheError) {
        debugPrint('⚠️ DATABASE: Cache query failed: $cacheError');
      }

      // If not in cache, try server or default
      final querySnapshot = await _firestore
          .collection(companiesCollection)
          .where('ownerCode', isEqualTo: ownerCode)
          .limit(1)
          .get();

      if (querySnapshot.docs.isNotEmpty) {
        final company = Company.fromSnapshot(querySnapshot.docs.first);
        debugPrint('✅ DATABASE: Found company: ${company.name} (${company.ownerCode})');
        return company;
      }

      debugPrint('❌ DATABASE: No company found with owner code: $ownerCode');
      return null;
    } catch (e) {
      debugPrint('❌ DATABASE: Error getting company by owner code: $e');
      return null;
    }
  }

  /// Get company by company code (for employee onboarding)
  Future<Company?> getCompanyByCompanyCode(String companyCode) async {
    try {
      debugPrint('🔍 DATABASE: Searching for company with company code: $companyCode');

      // Try to get from cache first (offline support)
      try {
        final cacheSnapshot = await _firestore
            .collection(companiesCollection)
            .where('companyCode', isEqualTo: companyCode)
            .limit(1)
            .get(const GetOptions(source: Source.cache));

        if (cacheSnapshot.docs.isNotEmpty) {
          final company = Company.fromSnapshot(cacheSnapshot.docs.first);
          debugPrint('✅ DATABASE: Found company in cache: ${company.name} (${company.companyCode})');
          return company;
        }
        debugPrint('🔍 DATABASE: Not found in cache, trying server...');
      } catch (cacheError) {
        debugPrint('⚠️ DATABASE: Cache query failed: $cacheError');
      }

      // If not in cache, try server or default
      final querySnapshot = await _firestore
          .collection(companiesCollection)
          .where('companyCode', isEqualTo: companyCode)
          .limit(1)
          .get();

      if (querySnapshot.docs.isNotEmpty) {
        final company = Company.fromSnapshot(querySnapshot.docs.first);
        debugPrint('✅ DATABASE: Found company: ${company.name} (${company.companyCode})');
        return company;
      }

      debugPrint('❌ DATABASE: No company found with company code: $companyCode');
      return null;
    } catch (e) {
      debugPrint('❌ DATABASE: Error getting company by company code: $e');
      return null;
    }
  }

  /// Get all companies
  Future<List<Company>> getAllCompanies() async {
    try {
      final querySnapshot = await _firestore
          .collection(companiesCollection)
          .orderBy('name')
          .get();

      return querySnapshot.docs
          .map((doc) => Company.fromSnapshot(doc))
          .toList();
    } catch (e) {
      debugPrint('Error getting all companies: $e');
      return [];
    }
  }

  /// Get company by ID
  Future<Company?> getCompany(String companyId) async {
    try {
      final docSnapshot = await _firestore
          .collection(companiesCollection)
          .doc(companyId)
          .get();

      if (docSnapshot.exists) {
        return Company.fromSnapshot(docSnapshot);
      }
      return null;
    } catch (e) {
      debugPrint('Error getting company: $e');
      return null;
    }
  }

  /// Get company by access code
  Future<Company?> getCompanyByAccessCode(String accessCode) async {
    try {
      final querySnapshot = await _firestore
          .collection(companiesCollection)
          .where('accessCode', isEqualTo: accessCode)
          .limit(1)
          .get();

      if (querySnapshot.docs.isNotEmpty) {
        return Company.fromSnapshot(querySnapshot.docs.first);
      }
      return null;
    } catch (e) {
      debugPrint('Error getting company by access code: $e');
      return null;
    }
  }

  /// Delete a company
  Future<bool> deleteCompany(String companyId) async {
    try {
      await _firestore.collection(companiesCollection).doc(companyId).delete();
      debugPrint('Company deleted successfully: $companyId');
      return true;
    } catch (e) {
      debugPrint('Error deleting company: $e');
      return false;
    }
  }

  /// Get employees by company ID
  Future<List<Employee>> getEmployeesByCompany(String companyId) async {
    try {
      debugPrint('🔍 DATABASE: Fetching employees for company ID: $companyId');

      // Try cache first for offline support
      try {
        final cacheSnapshot = await _firestore
            .collection(employeesCollection)
            .where('companyId', isEqualTo: companyId)
            .orderBy('createdAt', descending: true)
            .get(const GetOptions(source: Source.cache));

        if (cacheSnapshot.docs.isNotEmpty) {
          final employees = cacheSnapshot.docs
              .map((doc) => Employee.fromSnapshot(doc))
              .toList();
          debugPrint('✅ DATABASE: Found ${employees.length} employees in cache');
          for (final emp in employees) {
            debugPrint('   - ${emp.name} (${emp.designation}) - Company: ${emp.companyId}');
          }
          return employees;
        }
        debugPrint('🔍 DATABASE: No employees found in cache, trying server...');
      } catch (cacheError) {
        debugPrint('⚠️ DATABASE: Cache query failed: $cacheError');
      }

      // Try server
      final querySnapshot = await _firestore
          .collection(employeesCollection)
          .where('companyId', isEqualTo: companyId)
          .orderBy('createdAt', descending: true)
          .get();

      final employees = querySnapshot.docs
          .map((doc) => Employee.fromSnapshot(doc))
          .toList();

      debugPrint('✅ DATABASE: Found ${employees.length} employees on server');
      for (final emp in employees) {
        debugPrint('   - ${emp.name} (${emp.designation}) - Company: ${emp.companyId}');
      }

      return employees;
    } catch (e) {
      debugPrint('❌ DATABASE: Error getting employees by company: $e');
      return [];
    }
  }

  /// Create a new employee
  Future<bool> createEmployee(Employee employee) async {
    try {
      debugPrint('🔍 DATABASE: Creating employee: ${employee.name}');
      debugPrint('   - Employee ID: ${employee.id}');
      debugPrint('   - Company ID: ${employee.companyId}');
      debugPrint('   - Company Name: ${employee.companyName}');
      debugPrint('   - Phone: ${employee.phoneNumber}');
      debugPrint('   - Designation: ${employee.designation}');

      await _firestore
          .collection(employeesCollection)
          .doc(employee.id)
          .set(employee.toMap());

      debugPrint('✅ DATABASE: Employee created successfully: ${employee.name}');

      // Verify the employee was actually saved to Firebase
      await _verifyEmployeeSync(employee.id, employee.name);

      return true;
    } catch (e) {
      debugPrint('❌ DATABASE: Error creating employee: $e');
      return false;
    }
  }

  /// Verify that an employee was properly synced to Firebase
  Future<void> _verifyEmployeeSync(String employeeId, String employeeName) async {
    try {
      debugPrint('🔍 SYNC VERIFICATION: Checking if employee was saved to Firebase...');

      // Wait a moment for the write to complete
      await Future.delayed(const Duration(milliseconds: 500));

      // Try to read the employee from server
      final doc = await _firestore
          .collection(employeesCollection)
          .doc(employeeId)
          .get(const GetOptions(source: Source.server));

      if (doc.exists) {
        final data = doc.data()!;
        debugPrint('✅ SYNC VERIFICATION: Employee successfully synced to Firebase');
        debugPrint('   - Name: ${data['name']}');
        debugPrint('   - Company ID: ${data['companyId']}');
        debugPrint('   - Phone: ${data['phoneNumber']}');
      } else {
        debugPrint('❌ SYNC VERIFICATION: Employee not found on Firebase server');
      }
    } catch (e) {
      debugPrint('⚠️ SYNC VERIFICATION: Could not verify sync (network issue?): $e');
    }
  }

  /// Get employee by ID
  Future<Employee?> getEmployeeById(String employeeId) async {
    try {
      final doc = await _firestore
          .collection(employeesCollection)
          .doc(employeeId)
          .get();

      if (doc.exists) {
        return Employee.fromSnapshot(doc);
      }
      return null;
    } catch (e) {
      debugPrint('Error getting employee by ID: $e');
      return null;
    }
  }

  /// Update company's companyCode field (for migration)
  Future<bool> updateCompanyCodeField(String companyId, String companyCode) async {
    try {
      debugPrint('🔧 DATABASE: Updating companyCode field for company: $companyId -> $companyCode');

      await _firestore
          .collection(companiesCollection)
          .doc(companyId)
          .update({
        'companyCode': companyCode,
        'updatedAt': FieldValue.serverTimestamp(),
      });

      debugPrint('✅ DATABASE: Successfully updated companyCode field');
      return true;
    } catch (e) {
      debugPrint('❌ DATABASE: Error updating companyCode field: $e');
      return false;
    }
  }

  /// Get employee by phone number (for login)
  Future<Employee?> getEmployeeByPhoneNumber(String phoneNumber) async {
    try {
      debugPrint('🔍 DATABASE: Searching for employee with phone number: $phoneNumber');

      // Try to get from cache first (offline support)
      try {
        final cacheSnapshot = await _firestore
            .collection(employeesCollection)
            .where('phoneNumber', isEqualTo: phoneNumber)
            .limit(1)
            .get(const GetOptions(source: Source.cache));

        if (cacheSnapshot.docs.isNotEmpty) {
          final employee = Employee.fromSnapshot(cacheSnapshot.docs.first);
          debugPrint('✅ DATABASE: Found employee in cache: ${employee.name} (${employee.phoneNumber})');
          return employee;
        }
        debugPrint('🔍 DATABASE: Not found in cache, trying server...');
      } catch (cacheError) {
        debugPrint('⚠️ DATABASE: Cache query failed: $cacheError');
      }

      // If not in cache, try server or default
      final querySnapshot = await _firestore
          .collection(employeesCollection)
          .where('phoneNumber', isEqualTo: phoneNumber)
          .limit(1)
          .get();

      if (querySnapshot.docs.isNotEmpty) {
        final employee = Employee.fromSnapshot(querySnapshot.docs.first);
        debugPrint('✅ DATABASE: Found employee: ${employee.name} (${employee.phoneNumber})');
        return employee;
      }

      debugPrint('❌ DATABASE: No employee found with phone number: $phoneNumber');
      return null;
    } catch (e) {
      debugPrint('❌ DATABASE: Error getting employee by phone number: $e');
      return null;
    }
  }

  /// Get company by ID
  Future<Company?> getCompanyById(String companyId) async {
    try {
      final doc = await _firestore
          .collection(companiesCollection)
          .doc(companyId)
          .get();

      if (doc.exists) {
        return Company.fromSnapshot(doc);
      }
      return null;
    } catch (e) {
      debugPrint('Error getting company by ID: $e');
      return null;
    }
  }

  /// Search companies by name
  Future<List<Company>> searchCompanies(String searchTerm) async {
    try {
      final querySnapshot = await _firestore
          .collection(companiesCollection)
          .orderBy('name')
          .startAt([searchTerm])
          .endAt(['$searchTerm\uf8ff'])
          .get();

      return querySnapshot.docs
          .map((doc) => Company.fromSnapshot(doc))
          .toList();
    } catch (e) {
      debugPrint('Error searching companies: $e');
      return [];
    }
  }

  // ==================== EMPLOYEE OPERATIONS ====================

  /// Get today's attendance for an employee
  Future<Attendance?> getTodayAttendance(String employeeId) async {
    try {
      final today = DateTime.now();
      final startOfDay = DateTime(today.year, today.month, today.day);
      final endOfDay = startOfDay.add(const Duration(days: 1));

      final querySnapshot = await _firestore
          .collection(attendanceRecordsCollection)
          .where('employeeId', isEqualTo: employeeId)
          .where('date', isGreaterThanOrEqualTo: Timestamp.fromDate(startOfDay))
          .where('date', isLessThan: Timestamp.fromDate(endOfDay))
          .limit(1)
          .get();

      if (querySnapshot.docs.isNotEmpty) {
        return Attendance.fromSnapshot(querySnapshot.docs.first);
      }
      return null;
    } catch (e) {
      debugPrint('Error getting today\'s attendance: $e');
      return null;
    }
  }

  /// Create attendance record
  Future<bool> createAttendance(Attendance attendance) async {
    try {
      await _firestore
          .collection(attendanceRecordsCollection)
          .add(attendance.toMap());

      debugPrint('Attendance created successfully for: ${attendance.employeeName}');
      return true;
    } catch (e) {
      debugPrint('Error creating attendance: $e');
      return false;
    }
  }

  /// Update attendance record
  Future<bool> updateAttendance(Attendance attendance) async {
    try {
      await _firestore
          .collection(attendanceRecordsCollection)
          .doc(attendance.id)
          .update(attendance.toMap());

      debugPrint('Attendance updated successfully for: ${attendance.employeeName}');
      return true;
    } catch (e) {
      debugPrint('Error updating attendance: $e');
      return false;
    }
  }

  /// Get attendance history for an employee
  Future<List<Attendance>> getAttendanceHistory(String employeeId, {int limit = 30}) async {
    try {
      final querySnapshot = await _firestore
          .collection(attendanceRecordsCollection)
          .where('employeeId', isEqualTo: employeeId)
          .orderBy('date', descending: true)
          .limit(limit)
          .get();

      return querySnapshot.docs
          .map((doc) => Attendance.fromSnapshot(doc))
          .toList();
    } catch (e) {
      debugPrint('Error getting attendance history: $e');
      return [];
    }
  }

  /// Update employee's current location for real-time tracking
  Future<bool> updateEmployeeLocation(
    String employeeId,
    double latitude,
    double longitude,
    DateTime timestamp,
    bool isInOffice,
  ) async {
    try {
      await _firestore
          .collection(employeesCollection)
          .doc(employeeId)
          .update({
        'currentLatitude': latitude,
        'currentLongitude': longitude,
        'lastLocationUpdate': Timestamp.fromDate(timestamp),
        'isInOffice': isInOffice,
        'updatedAt': Timestamp.fromDate(DateTime.now()),
      });

      // Also store in location history
      await _firestore
          .collection('employee_locations')
          .add({
        'employeeId': employeeId,
        'latitude': latitude,
        'longitude': longitude,
        'timestamp': Timestamp.fromDate(timestamp),
        'isInOffice': isInOffice,
        'createdAt': Timestamp.fromDate(DateTime.now()),
      });

      return true;
    } catch (e) {
      debugPrint('Error updating employee location: $e');
      return false;
    }
  }

  /// Get real-time location of employees in a company
  Stream<List<Map<String, dynamic>>> getEmployeeLocationsStream(String companyId) {
    return _firestore
        .collection(employeesCollection)
        .where('companyId', isEqualTo: companyId)
        .where('isActive', isEqualTo: true)
        .snapshots()
        .map((snapshot) {
      return snapshot.docs.map((doc) {
        final data = doc.data();
        return {
          'employeeId': doc.id,
          'name': data['name'] ?? '',
          'designation': data['designation'] ?? '',
          'currentLatitude': data['currentLatitude'],
          'currentLongitude': data['currentLongitude'],
          'lastLocationUpdate': data['lastLocationUpdate'],
          'isInOffice': data['isInOffice'] ?? false,
        };
      }).where((employee) =>
        employee['currentLatitude'] != null &&
        employee['currentLongitude'] != null
      ).toList();
    });
  }

  /// Get location history for an employee
  Future<List<Map<String, dynamic>>> getEmployeeLocationHistory(
    String employeeId, {
    DateTime? startDate,
    DateTime? endDate,
    int limit = 100,
  }) async {
    try {
      Query query = _firestore
          .collection('employee_locations')
          .where('employeeId', isEqualTo: employeeId);

      if (startDate != null) {
        query = query.where('timestamp', isGreaterThanOrEqualTo: Timestamp.fromDate(startDate));
      }

      if (endDate != null) {
        query = query.where('timestamp', isLessThanOrEqualTo: Timestamp.fromDate(endDate));
      }

      final querySnapshot = await query
          .orderBy('timestamp', descending: true)
          .limit(limit)
          .get();

      return querySnapshot.docs.map((doc) {
        final data = doc.data() as Map<String, dynamic>;
        return {
          'latitude': data['latitude'],
          'longitude': data['longitude'],
          'timestamp': (data['timestamp'] as Timestamp).toDate(),
          'isInOffice': data['isInOffice'] ?? false,
        };
      }).toList();
    } catch (e) {
      debugPrint('Error getting employee location history: $e');
      return [];
    }
  }

  /// Get employees by company ID
  Future<List<Employee>> getEmployeesByCompanyId(String companyId) async {
    try {
      final querySnapshot = await _firestore
          .collection(employeesCollection)
          .where('companyId', isEqualTo: companyId)
          .where('isActive', isEqualTo: true)
          .orderBy('name')
          .get();

      return querySnapshot.docs
          .map((doc) => Employee.fromSnapshot(doc))
          .toList();
    } catch (e) {
      debugPrint('Error getting employees by company ID: $e');
      return [];
    }
  }

  /// Search employees by name in a company
  Future<List<Employee>> searchEmployeesInCompany(String companyId, String searchTerm) async {
    try {
      final querySnapshot = await _firestore
          .collection(employeesCollection)
          .where('companyId', isEqualTo: companyId)
          .where('isActive', isEqualTo: true)
          .orderBy('name')
          .startAt([searchTerm])
          .endAt(['$searchTerm\uf8ff'])
          .get();

      return querySnapshot.docs
          .map((doc) => Employee.fromSnapshot(doc))
          .toList();
    } catch (e) {
      debugPrint('Error searching employees: $e');
      return [];
    }
  }

  /// Check if employee ID exists in company
  Future<bool> isEmployeeIdExists(String employeeId, String companyId) async {
    try {
      final querySnapshot = await _firestore
          .collection(employeesCollection)
          .where('employeeId', isEqualTo: employeeId)
          .where('companyId', isEqualTo: companyId)
          .limit(1)
          .get();

      return querySnapshot.docs.isNotEmpty;
    } catch (e) {
      debugPrint('Error checking employee ID existence: $e');
      return false;
    }
  }

  // ==================== ATTENDANCE OPERATIONS ====================

  /// Create attendance record
  Future<String?> createAttendanceRecord(AttendanceRecord record) async {
    try {
      final docRef = await _firestore.collection(attendanceRecordsCollection).add(record.toMap());
      return docRef.id;
    } catch (e) {
      debugPrint('Error creating attendance record: $e');
      return null;
    }
  }

  /// Get attendance records for employee on specific date
  Future<List<AttendanceRecord>> getAttendanceRecordsForDate(
    String employeeId,
    DateTime date,
  ) async {
    try {
      final startOfDay = DateTime(date.year, date.month, date.day);
      final endOfDay = DateTime(date.year, date.month, date.day, 23, 59, 59);

      final querySnapshot = await _firestore
          .collection(attendanceRecordsCollection)
          .where('employeeId', isEqualTo: employeeId)
          .where('timestamp', isGreaterThanOrEqualTo: Timestamp.fromDate(startOfDay))
          .where('timestamp', isLessThanOrEqualTo: Timestamp.fromDate(endOfDay))
          .orderBy('timestamp')
          .get();

      return querySnapshot.docs
          .map((doc) => AttendanceRecord.fromSnapshot(doc))
          .toList();
    } catch (e) {
      debugPrint('Error getting attendance records for date: $e');
      return [];
    }
  }

  /// Get latest attendance record for employee
  Future<AttendanceRecord?> getLatestAttendanceRecord(String employeeId) async {
    try {
      final querySnapshot = await _firestore
          .collection(attendanceRecordsCollection)
          .where('employeeId', isEqualTo: employeeId)
          .orderBy('timestamp', descending: true)
          .limit(1)
          .get();

      if (querySnapshot.docs.isNotEmpty) {
        return AttendanceRecord.fromSnapshot(querySnapshot.docs.first);
      }
      return null;
    } catch (e) {
      debugPrint('Error getting latest attendance record: $e');
      return null;
    }
  }

  /// Create or update daily attendance
  Future<String?> createOrUpdateDailyAttendance(DailyAttendance dailyAttendance) async {
    try {
      final dateString = '${dailyAttendance.date.year}-${dailyAttendance.date.month.toString().padLeft(2, '0')}-${dailyAttendance.date.day.toString().padLeft(2, '0')}';
      final docId = '${dailyAttendance.employeeId}_$dateString';

      await _firestore
          .collection(dailyAttendanceCollection)
          .doc(docId)
          .set(dailyAttendance.toMap(), SetOptions(merge: true));

      return docId;
    } catch (e) {
      debugPrint('Error creating/updating daily attendance: $e');
      return null;
    }
  }

  /// Get daily attendance for employee on specific date
  Future<DailyAttendance?> getDailyAttendance(String employeeId, DateTime date) async {
    try {
      final dateString = '${date.year}-${date.month.toString().padLeft(2, '0')}-${date.day.toString().padLeft(2, '0')}';
      final docId = '${employeeId}_$dateString';

      final doc = await _firestore
          .collection(dailyAttendanceCollection)
          .doc(docId)
          .get();

      if (doc.exists) {
        return DailyAttendance.fromSnapshot(doc);
      }
      return null;
    } catch (e) {
      debugPrint('Error getting daily attendance: $e');
      return null;
    }
  }

  /// Get daily attendance for all employees in company on specific date
  Future<List<DailyAttendance>> getDailyAttendanceForCompany(
    String companyId,
    DateTime date,
  ) async {
    try {
      final startOfDay = DateTime(date.year, date.month, date.day);
      final endOfDay = DateTime(date.year, date.month, date.day, 23, 59, 59);

      final querySnapshot = await _firestore
          .collection(dailyAttendanceCollection)
          .where('companyId', isEqualTo: companyId)
          .where('date', isGreaterThanOrEqualTo: Timestamp.fromDate(startOfDay))
          .where('date', isLessThanOrEqualTo: Timestamp.fromDate(endOfDay))
          .get();

      return querySnapshot.docs
          .map((doc) => DailyAttendance.fromSnapshot(doc))
          .toList();
    } catch (e) {
      debugPrint('Error getting daily attendance for company: $e');
      return [];
    }
  }

  /// Get attendance records for date range
  Future<List<DailyAttendance>> getAttendanceForDateRange(
    String employeeId,
    DateTime startDate,
    DateTime endDate,
  ) async {
    try {
      final querySnapshot = await _firestore
          .collection(dailyAttendanceCollection)
          .where('employeeId', isEqualTo: employeeId)
          .where('date', isGreaterThanOrEqualTo: Timestamp.fromDate(startDate))
          .where('date', isLessThanOrEqualTo: Timestamp.fromDate(endDate))
          .orderBy('date', descending: true)
          .get();

      return querySnapshot.docs
          .map((doc) => DailyAttendance.fromSnapshot(doc))
          .toList();
    } catch (e) {
      debugPrint('Error getting attendance for date range: $e');
      return [];
    }
  }

  // ==================== UTILITY METHODS ====================

  /// Generate unique company owner code
  Future<String> generateUniqueCompanyCode() async {
    String code;
    bool isUnique = false;
    int attempts = 0;
    const maxAttempts = 10;

    do {
      code = _codeGenerator.generateCompanyOwnerCode();
      final existingCompany = await getCompanyByOwnerCode(code);
      isUnique = existingCompany == null;
      attempts++;
    } while (!isUnique && attempts < maxAttempts);

    if (!isUnique) {
      throw Exception('Failed to generate unique company code after $maxAttempts attempts');
    }

    return code;
  }

  /// Comprehensive Firebase sync verification
  Future<void> verifyFirebaseSync() async {
    try {
      debugPrint('🔍 FIREBASE SYNC: Starting comprehensive sync verification...');

      // Check companies
      await _verifyCompaniesSync();

      // Check employees
      await _verifyEmployeesSync();

      debugPrint('✅ FIREBASE SYNC: Verification completed');
    } catch (e) {
      debugPrint('❌ FIREBASE SYNC: Verification failed: $e');
    }
  }

  Future<void> _verifyCompaniesSync() async {
    try {
      debugPrint('🔍 COMPANIES SYNC: Verifying companies...');

      final snapshot = await _firestore
          .collection(companiesCollection)
          .get(const GetOptions(source: Source.server));

      debugPrint('✅ COMPANIES SYNC: Found ${snapshot.docs.length} companies on server');

      for (final doc in snapshot.docs) {
        final data = doc.data();
        debugPrint('   - ${data['name']} (Owner: ${data['ownerCode']}, Company: ${data['companyCode']})');
      }
    } catch (e) {
      debugPrint('❌ COMPANIES SYNC: Error verifying companies: $e');
    }
  }

  Future<void> _verifyEmployeesSync() async {
    try {
      debugPrint('🔍 EMPLOYEES SYNC: Verifying employees...');

      final snapshot = await _firestore
          .collection(employeesCollection)
          .get(const GetOptions(source: Source.server));

      debugPrint('✅ EMPLOYEES SYNC: Found ${snapshot.docs.length} employees on server');

      for (final doc in snapshot.docs) {
        final data = doc.data();
        debugPrint('   - ${data['name']} (${data['designation']}) - Company: ${data['companyName']} (ID: ${data['companyId']})');
      }
    } catch (e) {
      debugPrint('❌ EMPLOYEES SYNC: Error verifying employees: $e');
    }
  }
}
