import 'dart:math';

class CodeGeneratorService {
  static final CodeGeneratorService _instance = CodeGeneratorService._internal();
  factory CodeGeneratorService() => _instance;
  CodeGeneratorService._internal();

  final Random _random = Random();

  /// Generate a secure random alphanumeric code
  /// Excludes confusing characters like 0, O, 1, I, l
  String generateSecureCode(int length) {
    const chars = 'ABCDEFGHJKMNPQRSTUVWXYZ23456789';
    String result = '';
    
    for (int i = 0; i < length; i++) {
      result += chars[_random.nextInt(chars.length)];
    }
    
    return result;
  }

  /// Generate company owner code (10 characters)
  String generateCompanyOwnerCode() {
    return generateSecureCode(10);
  }

  /// Generate company code for employees (6 characters)
  String generateCompanyCode() {
    return generateSecureCode(6);
  }

  /// Generate super admin access code (8 characters)
  String generateSuperAdminCode() {
    return generateSecureCode(8);
  }

  /// Validate code format
  bool isValidCodeFormat(String code, int expectedLength) {
    if (code.length != expectedLength) return false;
    
    // Check if code contains only allowed characters
    const allowedChars = 'ABCDEFGHJKMNPQRSTUVWXYZ23456789';
    for (int i = 0; i < code.length; i++) {
      if (!allowedChars.contains(code[i])) {
        return false;
      }
    }
    
    return true;
  }

  /// Format code with dashes for better readability
  String formatCodeWithDashes(String code) {
    if (code.length == 10) {
      // Format as XXX-XXX-XXXX
      return '${code.substring(0, 3)}-${code.substring(3, 6)}-${code.substring(6)}';
    } else if (code.length == 8) {
      // Format as XXXX-XXXX
      return '${code.substring(0, 4)}-${code.substring(4)}';
    }
    return code;
  }

  /// Remove dashes from formatted code
  String removeFormatting(String formattedCode) {
    return formattedCode.replaceAll('-', '').toUpperCase();
  }
}
