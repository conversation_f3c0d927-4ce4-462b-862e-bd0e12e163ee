import 'dart:io';
import 'package:flutter/foundation.dart';
import 'package:firebase_core/firebase_core.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:firebase_messaging/firebase_messaging.dart';
import 'package:firebase_analytics/firebase_analytics.dart';
import 'package:firebase_crashlytics/firebase_crashlytics.dart';
import 'package:firebase_storage/firebase_storage.dart';
import 'package:firebase_remote_config/firebase_remote_config.dart';

class FirebaseService {
  static final FirebaseService _instance = FirebaseService._internal();
  factory FirebaseService() => _instance;
  FirebaseService._internal();

  // Firebase instances
  late FirebaseAuth _auth;
  late FirebaseFirestore _firestore;
  late FirebaseMessaging _messaging;
  late FirebaseAnalytics _analytics;
  late FirebaseCrashlytics _crashlytics;
  late FirebaseStorage _storage;
  late FirebaseRemoteConfig _remoteConfig;

  // Getters
  FirebaseAuth get auth => _auth;
  FirebaseFirestore get firestore => _firestore;
  FirebaseMessaging get messaging => _messaging;
  FirebaseAnalytics get analytics => _analytics;
  FirebaseCrashlytics get crashlytics => _crashlytics;
  FirebaseStorage get storage => _storage;
  FirebaseRemoteConfig get remoteConfig => _remoteConfig;

  /// Initialize Firebase and all services
  Future<void> initialize() async {
    try {
      // Initialize Firebase Core
      await Firebase.initializeApp();
      
      // Initialize all Firebase services
      _auth = FirebaseAuth.instance;
      _firestore = FirebaseFirestore.instance;
      _messaging = FirebaseMessaging.instance;
      _analytics = FirebaseAnalytics.instance;
      _crashlytics = FirebaseCrashlytics.instance;
      _storage = FirebaseStorage.instance;
      _remoteConfig = FirebaseRemoteConfig.instance;

      // Configure services
      await _configureFirebaseServices();
      
      debugPrint('✅ Firebase initialized successfully');
    } catch (e) {
      debugPrint('❌ Firebase initialization failed: $e');
      // Log to crashlytics if available
      try {
        await _crashlytics.recordError(e, null);
      } catch (_) {
        // Crashlytics not initialized yet
      }
      rethrow;
    }
  }

  /// Configure all Firebase services
  Future<void> _configureFirebaseServices() async {
    await Future.wait([
      _configureAuth(),
      _configureFirestore(),
      _configureMessaging(),
      _configureAnalytics(),
      _configureCrashlytics(),
      _configureRemoteConfig(),
    ]);
  }

  /// Configure Firebase Authentication
  Future<void> _configureAuth() async {
    try {
      // Enable persistence only on web platforms
      if (kIsWeb) {
        await _auth.setPersistence(Persistence.LOCAL);
      }

      // Listen to auth state changes
      _auth.authStateChanges().listen((User? user) {
        if (user != null) {
          debugPrint('User signed in: ${user.uid}');
          _analytics.setUserId(id: user.uid);
        } else {
          debugPrint('User signed out');
          _analytics.setUserId(id: null);
        }
      });

      debugPrint('✅ Firebase Auth configured');
    } catch (e) {
      debugPrint('❌ Firebase Auth configuration failed: $e');
    }
  }

  /// Configure Firestore
  Future<void> _configureFirestore() async {
    try {
      // Enable offline persistence
      _firestore.settings = const Settings(
        persistenceEnabled: true,
        cacheSizeBytes: Settings.CACHE_SIZE_UNLIMITED,
      );
      
      debugPrint('✅ Firestore configured');
    } catch (e) {
      debugPrint('❌ Firestore configuration failed: $e');
    }
  }

  /// Configure Firebase Messaging (Push Notifications)
  Future<void> _configureMessaging() async {
    try {
      // Request permission for notifications
      NotificationSettings settings = await _messaging.requestPermission(
        alert: true,
        announcement: false,
        badge: true,
        carPlay: false,
        criticalAlert: false,
        provisional: false,
        sound: true,
      );

      if (settings.authorizationStatus == AuthorizationStatus.authorized) {
        debugPrint('✅ FCM permission granted');
        
        // Get FCM token
        String? token = await _messaging.getToken();
        debugPrint('FCM Token: $token');
        
        // Listen to token refresh
        _messaging.onTokenRefresh.listen((String token) {
          debugPrint('FCM Token refreshed: $token');
          // TODO: Send token to your server
        });
        
        // Handle foreground messages
        FirebaseMessaging.onMessage.listen((RemoteMessage message) {
          debugPrint('Received foreground message: ${message.notification?.title}');
          // TODO: Show local notification
        });
        
        // Handle background messages
        FirebaseMessaging.onBackgroundMessage(_firebaseMessagingBackgroundHandler);
        
      } else {
        debugPrint('❌ FCM permission denied');
      }
      
      debugPrint('✅ Firebase Messaging configured');
    } catch (e) {
      debugPrint('❌ Firebase Messaging configuration failed: $e');
    }
  }

  /// Configure Firebase Analytics
  Future<void> _configureAnalytics() async {
    try {
      // Enable analytics collection
      await _analytics.setAnalyticsCollectionEnabled(true);
      
      // Set user properties
      await _analytics.setUserProperty(name: 'app_version', value: '1.0.0');
      await _analytics.setUserProperty(
        name: 'platform',
        value: Platform.isAndroid ? 'android' : 'ios',
      );
      
      debugPrint('✅ Firebase Analytics configured');
    } catch (e) {
      debugPrint('❌ Firebase Analytics configuration failed: $e');
    }
  }

  /// Configure Firebase Crashlytics
  Future<void> _configureCrashlytics() async {
    try {
      // Enable crashlytics collection
      await _crashlytics.setCrashlyticsCollectionEnabled(true);
      
      // Set up Flutter error handling
      FlutterError.onError = (errorDetails) {
        _crashlytics.recordFlutterFatalError(errorDetails);
      };
      
      // Set up platform error handling
      PlatformDispatcher.instance.onError = (error, stack) {
        _crashlytics.recordError(error, stack, fatal: true);
        return true;
      };
      
      debugPrint('✅ Firebase Crashlytics configured');
    } catch (e) {
      debugPrint('❌ Firebase Crashlytics configuration failed: $e');
    }
  }

  /// Configure Firebase Remote Config
  Future<void> _configureRemoteConfig() async {
    try {
      // Set config settings
      await _remoteConfig.setConfigSettings(RemoteConfigSettings(
        fetchTimeout: const Duration(minutes: 1),
        minimumFetchInterval: const Duration(hours: 1),
      ));
      
      // Set default parameters
      await _remoteConfig.setDefaults({
        'app_name': 'timeSync',
        'maintenance_mode': false,
        'min_app_version': '1.0.0',
        'feature_flags': '{}',
      });
      
      // Fetch and activate
      await _remoteConfig.fetchAndActivate();
      
      debugPrint('✅ Firebase Remote Config configured');
    } catch (e) {
      debugPrint('❌ Firebase Remote Config configuration failed: $e');
    }
  }

  /// Sign in anonymously
  Future<User?> signInAnonymously() async {
    try {
      UserCredential result = await _auth.signInAnonymously();
      await _analytics.logEvent(name: 'anonymous_sign_in');
      return result.user;
    } catch (e) {
      await _crashlytics.recordError(e, null);
      debugPrint('Anonymous sign in failed: $e');
      return null;
    }
  }

  /// Sign out
  Future<void> signOut() async {
    try {
      await _auth.signOut();
      await _analytics.logEvent(name: 'sign_out');
    } catch (e) {
      await _crashlytics.recordError(e, null);
      debugPrint('Sign out failed: $e');
    }
  }

  /// Log custom event
  Future<void> logEvent(String name, {Map<String, Object>? parameters}) async {
    try {
      await _analytics.logEvent(name: name, parameters: parameters);
    } catch (e) {
      debugPrint('Failed to log event: $e');
    }
  }

  /// Log custom error
  Future<void> logError(dynamic error, StackTrace? stackTrace, {String? reason}) async {
    try {
      await _crashlytics.recordError(error, stackTrace, reason: reason);
    } catch (e) {
      debugPrint('Failed to log error: $e');
    }
  }

  /// Get current user
  User? get currentUser => _auth.currentUser;

  /// Check if user is signed in
  bool get isSignedIn => _auth.currentUser != null;

  /// Get FCM token
  Future<String?> getFCMToken() async {
    try {
      return await _messaging.getToken();
    } catch (e) {
      debugPrint('Failed to get FCM token: $e');
      return null;
    }
  }

  /// Get remote config value
  String getRemoteConfigString(String key) {
    try {
      return _remoteConfig.getString(key);
    } catch (e) {
      debugPrint('Failed to get remote config value: $e');
      return '';
    }
  }

  /// Get remote config bool
  bool getRemoteConfigBool(String key) {
    try {
      return _remoteConfig.getBool(key);
    } catch (e) {
      debugPrint('Failed to get remote config bool: $e');
      return false;
    }
  }
}

/// Background message handler (must be top-level function)
@pragma('vm:entry-point')
Future<void> _firebaseMessagingBackgroundHandler(RemoteMessage message) async {
  await Firebase.initializeApp();
  debugPrint('Handling background message: ${message.messageId}');
}
