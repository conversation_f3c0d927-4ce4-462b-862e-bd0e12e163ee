import 'dart:async';
import 'package:flutter/foundation.dart';
import 'package:geolocator/geolocator.dart';

import '../models/company.dart';
import '../models/employee.dart';
import '../models/attendance.dart';
import 'database_service.dart';

class LocationTrackingService {
  static final LocationTrackingService _instance = LocationTrackingService._internal();
  factory LocationTrackingService() => _instance;
  LocationTrackingService._internal();

  final DatabaseService _databaseService = DatabaseService();
  
  StreamSubscription<Position>? _positionStreamSubscription;
  Timer? _locationUpdateTimer;
  
  bool _isTracking = false;
  bool _isInOffice = false;
  Company? _currentCompany;
  Employee? _currentEmployee;
  
  // Office boundary settings (in meters)
  static const double _officeRadius = 100.0; // 100 meters radius
  static const int _locationUpdateInterval = 30; // 30 seconds


  bool get isTracking => _isTracking;
  bool get isInOffice => _isInOffice;

  /// Initialize location tracking for an employee
  Future<bool> initializeTracking(Employee employee, Company company) async {
    try {
      _currentEmployee = employee;
      _currentCompany = company;
      
      // Check location permissions
      final hasPermission = await _checkLocationPermissions();
      if (!hasPermission) {
        debugPrint('❌ Location permissions not granted');
        return false;
      }

      debugPrint('✅ Location tracking initialized for ${employee.name}');
      return true;
    } catch (e) {
      debugPrint('❌ Failed to initialize location tracking: $e');
      return false;
    }
  }

  /// Start real-time location tracking
  Future<void> startTracking() async {
    if (_isTracking || _currentEmployee == null || _currentCompany == null) {
      return;
    }

    try {
      _isTracking = true;
      
      // Start position stream for real-time updates
      _positionStreamSubscription = Geolocator.getPositionStream(
        locationSettings: const LocationSettings(
          accuracy: LocationAccuracy.high,
          distanceFilter: 10, // Update every 10 meters
        ),
      ).listen(
        _onLocationUpdate,
        onError: (error) {
          debugPrint('❌ Location stream error: $error');
        },
      );

      // Start periodic location updates
      _locationUpdateTimer = Timer.periodic(
        const Duration(seconds: _locationUpdateInterval),
        (_) => _performLocationCheck(),
      );

      debugPrint('✅ Location tracking started');
    } catch (e) {
      debugPrint('❌ Failed to start location tracking: $e');
      _isTracking = false;
    }
  }

  /// Stop location tracking
  void stopTracking() {
    _isTracking = false;
    _positionStreamSubscription?.cancel();
    _locationUpdateTimer?.cancel();
    debugPrint('🛑 Location tracking stopped');
  }

  /// Handle location updates
  void _onLocationUpdate(Position position) async {
    if (!_isTracking || _currentCompany == null || _currentEmployee == null) {
      return;
    }

    try {
      // Check if employee is within office boundaries
      final isCurrentlyInOffice = _isWithinOfficeBoundary(position);
      
      // If office status changed, handle check-in/out
      if (isCurrentlyInOffice != _isInOffice) {
        _isInOffice = isCurrentlyInOffice;
        
        if (_isInOffice) {
          await _handleAutoCheckIn(position);
        } else {
          await _handleAutoCheckOut(position);
        }
      }

      // Update location in database for real-time tracking
      await _updateEmployeeLocation(position);
      
    } catch (e) {
      debugPrint('❌ Error handling location update: $e');
    }
  }

  /// Check if position is within office boundary
  bool _isWithinOfficeBoundary(Position position) {
    if (_currentCompany == null) return false;
    
    final distance = _calculateDistance(
      position.latitude,
      position.longitude,
      _currentCompany!.latitude,
      _currentCompany!.longitude,
    );
    
    return distance <= _officeRadius;
  }

  /// Calculate distance between two points in meters
  double _calculateDistance(double lat1, double lon1, double lat2, double lon2) {
    return Geolocator.distanceBetween(lat1, lon1, lat2, lon2);
  }

  /// Handle automatic check-in when entering office
  Future<void> _handleAutoCheckIn(Position position) async {
    try {
      // Check if already checked in today
      final todayAttendance = await _databaseService.getTodayAttendance(_currentEmployee!.id);
      
      if (todayAttendance == null) {
        // Create new attendance record
        final attendance = Attendance(
          id: '', // Will be set by Firestore
          employeeId: _currentEmployee!.id,
          employeeName: _currentEmployee!.name,
          companyId: _currentEmployee!.companyId,
          date: DateTime.now(),
          checkInTime: DateTime.now(),
          checkInLatitude: position.latitude,
          checkInLongitude: position.longitude,
          notes: 'Auto check-in (Geofencing)',
          createdAt: DateTime.now(),
          updatedAt: DateTime.now(),
        );

        final success = await _databaseService.createAttendance(attendance);
        if (success) {
          debugPrint('✅ Auto check-in successful for ${_currentEmployee!.name}');
          _showLocationNotification('Welcome to office!', 'You have been automatically checked in.');
        }
      }
    } catch (e) {
      debugPrint('❌ Auto check-in failed: $e');
    }
  }

  /// Handle automatic check-out when leaving office
  Future<void> _handleAutoCheckOut(Position position) async {
    try {
      // Get today's attendance
      final todayAttendance = await _databaseService.getTodayAttendance(_currentEmployee!.id);
      
      if (todayAttendance != null && todayAttendance.checkOutTime == null) {
        // Update attendance with check-out
        final updatedAttendance = todayAttendance.copyWith(
          checkOutTime: DateTime.now(),
          checkOutLatitude: position.latitude,
          checkOutLongitude: position.longitude,
          notes: '${todayAttendance.notes ?? ''} | Auto check-out (Geofencing)',
          updatedAt: DateTime.now(),
        );

        final success = await _databaseService.updateAttendance(updatedAttendance);
        if (success) {
          debugPrint('✅ Auto check-out successful for ${_currentEmployee!.name}');
          _showLocationNotification('Goodbye!', 'You have been automatically checked out.');
        }
      }
    } catch (e) {
      debugPrint('❌ Auto check-out failed: $e');
    }
  }

  /// Update employee's current location in database
  Future<void> _updateEmployeeLocation(Position position) async {
    try {
      // Store current location for real-time tracking
      await _databaseService.updateEmployeeLocation(
        _currentEmployee!.id,
        position.latitude,
        position.longitude,
        DateTime.now(),
        _isInOffice,
      );
    } catch (e) {
      debugPrint('❌ Failed to update employee location: $e');
    }
  }

  /// Perform periodic location check
  Future<void> _performLocationCheck() async {
    try {
      final position = await Geolocator.getCurrentPosition(
        locationSettings: const LocationSettings(
          accuracy: LocationAccuracy.high,
        ),
      );
      _onLocationUpdate(position);
    } catch (e) {
      debugPrint('❌ Periodic location check failed: $e');
    }
  }

  /// Check location permissions
  Future<bool> _checkLocationPermissions() async {
    try {
      bool serviceEnabled = await Geolocator.isLocationServiceEnabled();
      if (!serviceEnabled) {
        debugPrint('❌ Location services are disabled');
        return false;
      }

      LocationPermission permission = await Geolocator.checkPermission();
      if (permission == LocationPermission.denied) {
        permission = await Geolocator.requestPermission();
        if (permission == LocationPermission.denied) {
          debugPrint('❌ Location permissions are denied');
          return false;
        }
      }

      if (permission == LocationPermission.deniedForever) {
        debugPrint('❌ Location permissions are permanently denied');
        return false;
      }

      // Request background location permission for continuous tracking
      if (permission == LocationPermission.whileInUse) {
        // On Android, request background location
        permission = await Geolocator.requestPermission();
      }

      return true;
    } catch (e) {
      debugPrint('❌ Error checking location permissions: $e');
      return false;
    }
  }

  /// Show location-based notification
  void _showLocationNotification(String title, String message) {
    // TODO: Implement local notifications
    debugPrint('📍 $title: $message');
  }

  /// Get current distance from office
  Future<double?> getDistanceFromOffice() async {
    if (_currentCompany == null) return null;
    
    try {
      final position = await Geolocator.getCurrentPosition();
      return _calculateDistance(
        position.latitude,
        position.longitude,
        _currentCompany!.latitude,
        _currentCompany!.longitude,
      );
    } catch (e) {
      debugPrint('❌ Failed to get distance from office: $e');
      return null;
    }
  }

  /// Check if employee is currently in office
  Future<bool> isCurrentlyInOffice() async {
    try {
      final position = await Geolocator.getCurrentPosition();
      return _isWithinOfficeBoundary(position);
    } catch (e) {
      debugPrint('❌ Failed to check office status: $e');
      return false;
    }
  }

  /// Dispose resources
  void dispose() {
    stopTracking();
    _currentEmployee = null;
    _currentCompany = null;
  }
}
