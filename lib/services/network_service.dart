import 'dart:async';
import 'dart:io';
import 'package:connectivity_plus/connectivity_plus.dart';
import 'package:flutter/foundation.dart';

class NetworkService {
  static final NetworkService _instance = NetworkService._internal();
  factory NetworkService() => _instance;
  NetworkService._internal();

  final Connectivity _connectivity = Connectivity();
  StreamSubscription<List<ConnectivityResult>>? _connectivitySubscription;
  
  bool _isConnected = true;
  bool get isConnected => _isConnected;

  /// Initialize network monitoring
  Future<void> initialize() async {
    try {
      // Check initial connectivity
      await _checkConnectivity();
      
      // Listen to connectivity changes
      _connectivitySubscription = _connectivity.onConnectivityChanged.listen(
        (List<ConnectivityResult> results) {
          _updateConnectionStatus(results);
        },
      );
      
      debugPrint('✅ Network service initialized');
    } catch (e) {
      debugPrint('❌ Network service initialization failed: $e');
    }
  }

  /// Check current connectivity status
  Future<void> _checkConnectivity() async {
    try {
      final List<ConnectivityResult> results = await _connectivity.checkConnectivity();
      _updateConnectionStatus(results);
    } catch (e) {
      debugPrint('Error checking connectivity: $e');
      _isConnected = false;
    }
  }

  /// Update connection status based on connectivity results
  void _updateConnectionStatus(List<ConnectivityResult> results) {
    final bool wasConnected = _isConnected;
    
    // Check if any connection type is available
    _isConnected = results.any((result) => 
      result == ConnectivityResult.mobile ||
      result == ConnectivityResult.wifi ||
      result == ConnectivityResult.ethernet ||
      result == ConnectivityResult.vpn
    );

    if (wasConnected != _isConnected) {
      debugPrint('Network status changed: ${_isConnected ? 'Connected' : 'Disconnected'}');
    }
  }

  /// Test internet connectivity by pinging a reliable server
  Future<bool> hasInternetConnection() async {
    if (!_isConnected) return false;

    try {
      final result = await InternetAddress.lookup('google.com');
      return result.isNotEmpty && result[0].rawAddress.isNotEmpty;
    } on SocketException catch (_) {
      return false;
    } catch (e) {
      debugPrint('Error testing internet connection: $e');
      return false;
    }
  }

  /// Wait for internet connection with timeout
  Future<bool> waitForConnection({Duration timeout = const Duration(seconds: 10)}) async {
    final completer = Completer<bool>();
    Timer? timer;

    // Set timeout
    timer = Timer(timeout, () {
      if (!completer.isCompleted) {
        completer.complete(false);
      }
    });

    // Check connection periodically
    Timer.periodic(const Duration(milliseconds: 500), (periodicTimer) async {
      if (completer.isCompleted) {
        periodicTimer.cancel();
        return;
      }

      final hasConnection = await hasInternetConnection();
      if (hasConnection) {
        periodicTimer.cancel();
        timer?.cancel();
        if (!completer.isCompleted) {
          completer.complete(true);
        }
      }
    });

    return completer.future;
  }

  /// Get connection type as string
  String getConnectionType() {
    if (!_isConnected) return 'No Connection';
    
    // This is a simplified version - you might want to store the actual connection type
    return 'Connected';
  }

  /// Dispose resources
  void dispose() {
    _connectivitySubscription?.cancel();
  }
}
