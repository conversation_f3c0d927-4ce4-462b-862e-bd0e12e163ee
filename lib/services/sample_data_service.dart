import 'package:flutter/foundation.dart';
import '../models/company.dart';
import 'database_service.dart';

class SampleDataService {
  static final SampleDataService _instance = SampleDataService._internal();
  factory SampleDataService() => _instance;
  SampleDataService._internal();

  final DatabaseService _databaseService = DatabaseService();

  /// Sample companies data
  final List<Map<String, dynamic>> _sampleCompanies = [
    {
      'name': 'Apple Inc.',
      'pincode': '95014',
      'latitude': 37.3318,
      'longitude': -122.0312,
      'locationAddress': '1 Apple Park Way, Cupertino, CA 95014, USA',
      'ownerCode': 'APPLE2024',
      'companyCode': 'APL24',
    },
    {
      'name': 'Samsung Electronics',
      'pincode': '16677',
      'latitude': 37.2431,
      'longitude': 127.0017,
      'locationAddress': '129 Samsung-ro, Yeongtong-gu, Suwon-si, Gyeonggi-do, South Korea',
      'ownerCode': 'SAMSUNG24',
      'companyCode': 'SAM24',
    },
    {
      'name': 'Google LLC',
      'pincode': '94043',
      'latitude': 37.4220,
      'longitude': -122.0841,
      'locationAddress': '1600 Amphitheatre Parkway, Mountain View, CA 94043, USA',
      'ownerCode': 'GOOGLE2024',
      'companyCode': 'GOO24',
    },
    {
      'name': 'Tata Consultancy Services',
      'pincode': '400001',
      'latitude': 19.0760,
      'longitude': 72.8777,
      'locationAddress': 'Bombay House, 24 Homi Mody Street, Mumbai, Maharashtra 400001, India',
      'ownerCode': 'TCS2024',
      'companyCode': 'TCS24',
    },
    {
      'name': 'Microsoft Corporation',
      'pincode': '98052',
      'latitude': 47.6062,
      'longitude': -122.3321,
      'locationAddress': '1 Microsoft Way, Redmond, WA 98052, USA',
      'ownerCode': 'MSFT2024',
      'companyCode': 'MSF24',
    },
    {
      'name': 'Meta Platforms Inc.',
      'pincode': '94025',
      'latitude': 37.4845,
      'longitude': -122.1477,
      'locationAddress': '1 Hacker Way, Menlo Park, CA 94025, USA',
      'ownerCode': 'META2024',
      'companyCode': 'MET24',
    },
  ];

  /// Create all sample companies
  Future<List<String>> createSampleCompanies() async {
    final List<String> createdCompanyIds = [];
    
    try {
      debugPrint('🏢 Creating sample companies...');
      
      for (final companyData in _sampleCompanies) {
        // Check if company already exists
        final existingCompany = await _databaseService.getCompanyByOwnerCode(companyData['ownerCode']);
        
        if (existingCompany == null) {
          final company = Company(
            id: '', // Will be set by Firestore
            name: companyData['name'],
            pincode: companyData['pincode'],
            latitude: companyData['latitude'],
            longitude: companyData['longitude'],
            locationAddress: companyData['locationAddress'],
            ownerCode: companyData['ownerCode'],
            companyCode: companyData['companyCode'],
            createdAt: DateTime.now(),
            updatedAt: DateTime.now(),
          );

          final companyId = await _databaseService.createCompany(company);
          
          if (companyId != null) {
            createdCompanyIds.add(companyId);
            debugPrint('✅ Created company: ${company.name} with code: ${company.ownerCode}');
          } else {
            debugPrint('❌ Failed to create company: ${company.name}');
          }
        } else {
          debugPrint('⚠️ Company already exists: ${companyData['name']} with code: ${companyData['ownerCode']}');
        }
      }
      
      debugPrint('🎉 Sample companies creation completed. Created: ${createdCompanyIds.length} companies');
      return createdCompanyIds;
      
    } catch (e) {
      debugPrint('❌ Error creating sample companies: $e');
      return [];
    }
  }

  /// Create a single sample company by name
  Future<String?> createSampleCompany(String companyName) async {
    try {
      final companyData = _sampleCompanies.firstWhere(
        (company) => company['name'].toLowerCase().contains(companyName.toLowerCase()),
        orElse: () => {},
      );

      if (companyData.isEmpty) {
        debugPrint('❌ Sample company not found: $companyName');
        return null;
      }

      // Check if company already exists
      final existingCompany = await _databaseService.getCompanyByOwnerCode(companyData['ownerCode']);
      
      if (existingCompany != null) {
        debugPrint('⚠️ Company already exists: ${companyData['name']}');
        return existingCompany.id;
      }

      final company = Company(
        id: '', // Will be set by Firestore
        name: companyData['name'],
        pincode: companyData['pincode'],
        latitude: companyData['latitude'],
        longitude: companyData['longitude'],
        locationAddress: companyData['locationAddress'],
        ownerCode: companyData['ownerCode'],
        companyCode: companyData['companyCode'],
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
      );

      final companyId = await _databaseService.createCompany(company);
      
      if (companyId != null) {
        debugPrint('✅ Created sample company: ${company.name} with code: ${company.ownerCode}');
      }
      
      return companyId;
      
    } catch (e) {
      debugPrint('❌ Error creating sample company: $e');
      return null;
    }
  }

  /// Get all sample company codes for testing
  List<String> getSampleCompanyCodes() {
    return _sampleCompanies.map((company) => company['ownerCode'] as String).toList();
  }

  /// Get sample company data by name
  Map<String, dynamic>? getSampleCompanyData(String companyName) {
    try {
      return _sampleCompanies.firstWhere(
        (company) => company['name'].toLowerCase().contains(companyName.toLowerCase()),
      );
    } catch (e) {
      return null;
    }
  }

  /// Delete all sample companies (for testing cleanup)
  Future<bool> deleteSampleCompanies() async {
    try {
      debugPrint('🗑️ Deleting sample companies...');
      
      for (final companyData in _sampleCompanies) {
        final company = await _databaseService.getCompanyByOwnerCode(companyData['ownerCode']);
        if (company != null) {
          await _databaseService.deleteCompany(company.id);
          debugPrint('🗑️ Deleted company: ${company.name}');
        }
      }
      
      debugPrint('✅ Sample companies cleanup completed');
      return true;
      
    } catch (e) {
      debugPrint('❌ Error deleting sample companies: $e');
      return false;
    }
  }

  /// Check if sample companies exist
  Future<bool> sampleCompaniesExist() async {
    try {
      debugPrint('🔍 Checking ${_sampleCompanies.length} sample companies...');
      int foundCount = 0;

      for (final companyData in _sampleCompanies) {
        final ownerCode = companyData['ownerCode'];
        debugPrint('🔍 Checking company with code: $ownerCode');
        final company = await _databaseService.getCompanyByOwnerCode(ownerCode);
        if (company != null) {
          debugPrint('✅ Found existing sample company: ${company.name} (${company.ownerCode})');
          foundCount++;
        } else {
          debugPrint('❌ Company with code $ownerCode not found');
        }
      }

      final allExist = foundCount == _sampleCompanies.length;
      debugPrint('🔍 Sample companies exist: $allExist ($foundCount/${_sampleCompanies.length} found)');

      if (allExist) {
        debugPrint('ℹ️ All sample companies already exist, skipping creation');
      } else {
        debugPrint('ℹ️ Missing ${_sampleCompanies.length - foundCount} sample companies, will create them');
      }

      return allExist;
    } catch (e) {
      debugPrint('Error checking sample companies: $e');
      return false;
    }
  }

  /// Migrate existing companies to add missing companyCode field
  Future<bool> migrateCompanyCodeField() async {
    try {
      debugPrint('🔧 DATABASE MIGRATION: Starting companyCode field migration...');

      int updatedCount = 0;

      for (final companyData in _sampleCompanies) {
        final ownerCode = companyData['ownerCode'];
        final expectedCompanyCode = companyData['companyCode'];

        debugPrint('🔧 MIGRATION: Checking company with owner code: $ownerCode');

        // Get the existing company
        final existingCompany = await _databaseService.getCompanyByOwnerCode(ownerCode);

        if (existingCompany != null) {
          // Check if companyCode field is missing or incorrect
          if (existingCompany.companyCode != expectedCompanyCode) {
            debugPrint('🔧 MIGRATION: Updating companyCode for ${existingCompany.name}');
            debugPrint('   Current: "${existingCompany.companyCode}" -> Expected: "$expectedCompanyCode"');

            // Update the company with the correct companyCode
            await _databaseService.updateCompanyCodeField(existingCompany.id, expectedCompanyCode);
            updatedCount++;

            debugPrint('✅ MIGRATION: Updated ${existingCompany.name}');
          } else {
            debugPrint('✅ MIGRATION: ${existingCompany.name} already has correct companyCode');
          }
        } else {
          debugPrint('⚠️ MIGRATION: Company with owner code $ownerCode not found');
        }
      }

      debugPrint('🎉 DATABASE MIGRATION: Completed! Updated $updatedCount companies');
      return true;

    } catch (e) {
      debugPrint('❌ DATABASE MIGRATION: Error during migration: $e');
      return false;
    }
  }
}
