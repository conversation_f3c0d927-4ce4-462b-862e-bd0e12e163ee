import 'package:flutter/foundation.dart';

import 'package:shared_preferences/shared_preferences.dart';
import '../models/employee.dart';
import '../models/company.dart';

enum UserRole {
  employee,
  companyOwner,
  superAdmin,
  none,
}

class AppStateService extends ChangeNotifier {
  static final AppStateService _instance = AppStateService._internal();
  factory AppStateService() => _instance;
  AppStateService._internal();

  // Current user state
  UserRole _currentRole = UserRole.none;
  Employee? _currentEmployee;
  Company? _currentCompany;
  String? _currentOwnerCode;
  bool _isInitialized = false;

  // Getters
  UserRole get currentRole => _currentRole;
  Employee? get currentEmployee => _currentEmployee;
  Company? get currentCompany => _currentCompany;
  String? get currentOwnerCode => _currentOwnerCode;
  bool get isInitialized => _isInitialized;
  bool get isLoggedIn => _currentRole != UserRole.none;

  // SharedPreferences keys
  static const String _keyUserRole = 'user_role';
  static const String _keyEmployeeId = 'employee_id';
  static const String _keyCompanyId = 'company_id';
  static const String _keyOwnerCode = 'owner_code';

  /// Initialize app state from stored preferences
  Future<void> initialize() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      
      // Get stored role
      final roleString = prefs.getString(_keyUserRole);
      if (roleString != null) {
        _currentRole = UserRole.values.firstWhere(
          (role) => role.name == roleString,
          orElse: () => UserRole.none,
        );
      }

      // Get stored employee ID
      final employeeId = prefs.getString(_keyEmployeeId);
      if (employeeId != null && _currentRole == UserRole.employee) {
        // Load employee data from database
        // This will be implemented when we create the employee service
      }

      // Get stored company ID and owner code
      final companyId = prefs.getString(_keyCompanyId);
      final ownerCode = prefs.getString(_keyOwnerCode);
      if (companyId != null && ownerCode != null && _currentRole == UserRole.companyOwner) {
        // Load company data from database
        // This will be implemented when we create the company service
      }

      _isInitialized = true;
      notifyListeners();
    } catch (e) {
      debugPrint('Error initializing app state: $e');
      _isInitialized = true;
      notifyListeners();
    }
  }

  /// Set employee login state
  Future<void> setEmployeeLogin(Employee employee, Company company) async {
    try {
      _currentRole = UserRole.employee;
      _currentEmployee = employee;
      _currentCompany = company;
      _currentOwnerCode = null;

      // Save to preferences
      final prefs = await SharedPreferences.getInstance();
      await prefs.setString(_keyUserRole, UserRole.employee.name);
      await prefs.setString(_keyEmployeeId, employee.id);
      await prefs.setString(_keyCompanyId, company.id);
      await prefs.remove(_keyOwnerCode);

      notifyListeners();
    } catch (e) {
      debugPrint('Error setting employee login: $e');
    }
  }

  /// Set company owner login state
  Future<void> setCompanyOwnerLogin(Company company, String ownerCode) async {
    try {
      _currentRole = UserRole.companyOwner;
      _currentEmployee = null;
      _currentCompany = company;
      _currentOwnerCode = ownerCode;

      // Save to preferences
      final prefs = await SharedPreferences.getInstance();
      await prefs.setString(_keyUserRole, UserRole.companyOwner.name);
      await prefs.setString(_keyCompanyId, company.id);
      await prefs.setString(_keyOwnerCode, ownerCode);
      await prefs.remove(_keyEmployeeId);

      notifyListeners();
    } catch (e) {
      debugPrint('Error setting company owner login: $e');
    }
  }

  /// Set super admin login state
  Future<void> setSuperAdminLogin() async {
    try {
      _currentRole = UserRole.superAdmin;
      _currentEmployee = null;
      _currentCompany = null;
      _currentOwnerCode = null;

      // Save to preferences
      final prefs = await SharedPreferences.getInstance();
      await prefs.setString(_keyUserRole, UserRole.superAdmin.name);
      await prefs.remove(_keyEmployeeId);
      await prefs.remove(_keyCompanyId);
      await prefs.remove(_keyOwnerCode);

      notifyListeners();
    } catch (e) {
      debugPrint('Error setting super admin login: $e');
    }
  }

  /// Logout and clear all state
  Future<void> logout() async {
    try {
      _currentRole = UserRole.none;
      _currentEmployee = null;
      _currentCompany = null;
      _currentOwnerCode = null;

      // Clear preferences
      final prefs = await SharedPreferences.getInstance();
      await prefs.remove(_keyUserRole);
      await prefs.remove(_keyEmployeeId);
      await prefs.remove(_keyCompanyId);
      await prefs.remove(_keyOwnerCode);

      notifyListeners();
    } catch (e) {
      debugPrint('Error during logout: $e');
    }
  }

  /// Update current employee data
  void updateEmployee(Employee employee) {
    if (_currentRole == UserRole.employee) {
      _currentEmployee = employee;
      notifyListeners();
    }
  }

  /// Update current company data
  void updateCompany(Company company) {
    _currentCompany = company;
    notifyListeners();
  }

  /// Check if user has permission for specific role
  bool hasRole(UserRole role) {
    return _currentRole == role;
  }

  /// Check if user is employee
  bool get isEmployee => _currentRole == UserRole.employee;

  /// Check if user is company owner
  bool get isCompanyOwner => _currentRole == UserRole.companyOwner;

  /// Check if user is super admin
  bool get isSuperAdmin => _currentRole == UserRole.superAdmin;

  /// Get display name for current user
  String get displayName {
    switch (_currentRole) {
      case UserRole.employee:
        return _currentEmployee?.name ?? 'Employee';
      case UserRole.companyOwner:
        return _currentCompany?.name ?? 'Company Owner';
      case UserRole.superAdmin:
        return 'Super Admin';
      case UserRole.none:
        return 'Guest';
    }
  }

  /// Get role display name
  String getRoleDisplayName(UserRole role) {
    switch (role) {
      case UserRole.employee:
        return 'Employee';
      case UserRole.companyOwner:
        return 'Company Owner';
      case UserRole.superAdmin:
        return 'Super Admin';
      case UserRole.none:
        return 'Not Logged In';
    }
  }
}
