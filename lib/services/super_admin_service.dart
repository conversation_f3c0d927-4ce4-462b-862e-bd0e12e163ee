import 'package:flutter/foundation.dart';
import 'database_service.dart';
import 'code_generator_service.dart';
import 'sample_data_service.dart';

class SuperAdminService {
  static final SuperAdminService _instance = SuperAdminService._internal();
  factory SuperAdminService() => _instance;
  SuperAdminService._internal();

  final DatabaseService _databaseService = DatabaseService();
  final SampleDataService _sampleDataService = SampleDataService();
  final CodeGeneratorService _codeGenerator = CodeGeneratorService();

  // Default Super Admin access code (can be changed later)
  static const String defaultSuperAdminCode = 'ADMIN2024';

  /// Initialize Super Admin in Firebase if not exists
  Future<void> initializeSuperAdmin() async {
    try {
      final existingSuperAdmin = await _databaseService.getSuperAdmin();

      if (existingSuperAdmin == null) {
        // Create default Super Admin
        final success = await _databaseService.createOrUpdateSuperAdmin(defaultSuperAdminCode);
        if (success) {
          debugPrint('✅ Super Admin initialized with code: $defaultSuperAdminCode');

          // Auto-create sample companies for testing
          debugPrint('🏢 Auto-creating sample companies for testing...');
          final createdIds = await _sampleDataService.createSampleCompanies();
          if (createdIds.isNotEmpty) {
            debugPrint('✅ Created ${createdIds.length} sample companies automatically');
          } else {
            debugPrint('ℹ️ Sample companies already exist or creation skipped');
          }
        } else {
          debugPrint('❌ Failed to initialize Super Admin');
        }
      } else {
        debugPrint('✅ Super Admin already exists with code: ${existingSuperAdmin.accessCode}');

        // Check if sample companies exist, if not create them
        debugPrint('🔍 Checking if sample companies exist...');
        final sampleCompaniesExist = await _sampleDataService.sampleCompaniesExist();
        debugPrint('🔍 Sample companies exist: $sampleCompaniesExist');

        if (!sampleCompaniesExist) {
          debugPrint('🏢 Creating sample companies for testing...');
          final createdIds = await _sampleDataService.createSampleCompanies();
          if (createdIds.isNotEmpty) {
            debugPrint('✅ Created ${createdIds.length} sample companies');
          } else {
            debugPrint('⚠️ No sample companies were created');
          }
        } else {
          debugPrint('ℹ️ Sample companies already exist, skipping creation');

          // Run migration to ensure companyCode field is properly set
          debugPrint('🔧 Running database migration for companyCode field...');
          final migrationSuccess = await _sampleDataService.migrateCompanyCodeField();
          if (migrationSuccess) {
            debugPrint('✅ Database migration completed successfully');
          } else {
            debugPrint('⚠️ Database migration had issues');
          }
        }

        // Verify Firebase sync status
        debugPrint('🔍 Running Firebase sync verification...');
        await _databaseService.verifyFirebaseSync();
      }
    } catch (e) {
      debugPrint('❌ Error initializing Super Admin: $e');
    }
  }

  /// Verify Super Admin access code (case-sensitive)
  Future<bool> verifyAccessCode(String code) async {
    try {
      return await _databaseService.verifySuperAdminCode(code); // Keep original case
    } catch (e) {
      debugPrint('Error verifying Super Admin code: $e');
      return false;
    }
  }

  /// Update Super Admin access code (case-sensitive)
  Future<bool> updateAccessCode(String newCode) async {
    try {
      return await _databaseService.createOrUpdateSuperAdmin(newCode); // Keep original case
    } catch (e) {
      debugPrint('Error updating Super Admin code: $e');
      return false;
    }
  }

  /// Generate a new random access code
  String generateNewAccessCode() {
    return _codeGenerator.generateSuperAdminCode();
  }

  /// Get current Super Admin details
  Future<String?> getCurrentAccessCode() async {
    try {
      final superAdmin = await _databaseService.getSuperAdmin();
      return superAdmin?.accessCode;
    } catch (e) {
      debugPrint('Error getting current access code: $e');
      return null;
    }
  }
}
