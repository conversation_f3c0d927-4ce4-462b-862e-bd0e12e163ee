import 'package:flutter/material.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'firebase_service.dart';
import 'database_service.dart';

class AuthService {
  static final AuthService _instance = AuthService._internal();
  factory AuthService() => _instance;
  AuthService._internal();

  final FirebaseService _firebaseService = FirebaseService();

  /// Get current user
  User? get currentUser => _firebaseService.currentUser;

  /// Check if user is signed in
  bool get isSignedIn => _firebaseService.isSignedIn;

  /// Stream of auth state changes
  Stream<User?> get authStateChanges => _firebaseService.auth.authStateChanges();

  /// Sign in anonymously
  Future<AuthResult> signInAnonymously() async {
    try {
      final user = await _firebaseService.signInAnonymously();
      if (user != null) {
        await _createUserDocument(user);
        await _firebaseService.logEvent('anonymous_sign_in_success');
        return AuthResult.success(user);
      } else {
        return AuthResult.failure('Failed to sign in anonymously');
      }
    } catch (e) {
      await _firebaseService.logError(e, StackTrace.current, reason: 'Anonymous sign in failed');
      return AuthResult.failure(e.toString());
    }
  }

  /// Sign in with email and password
  Future<AuthResult> signInWithEmailAndPassword(String email, String password) async {
    try {
      final credential = await _firebaseService.auth.signInWithEmailAndPassword(
        email: email,
        password: password,
      );
      
      if (credential.user != null) {
        await _updateUserDocument(credential.user!);
        await _firebaseService.logEvent('email_sign_in_success');
        return AuthResult.success(credential.user!);
      } else {
        return AuthResult.failure('Sign in failed');
      }
    } on FirebaseAuthException catch (e) {
      await _firebaseService.logError(e, StackTrace.current, reason: 'Email sign in failed');
      return AuthResult.failure(_getAuthErrorMessage(e.code));
    } catch (e) {
      await _firebaseService.logError(e, StackTrace.current, reason: 'Email sign in failed');
      return AuthResult.failure(e.toString());
    }
  }

  /// Sign in with phone number and password (for employees)
  Future<AuthResult> signInWithPhoneAndPassword(String phoneNumber, String password) async {
    try {
      debugPrint('🔐 AUTH SERVICE: Attempting phone login for: $phoneNumber');

      // Import DatabaseService to find employee by phone
      final databaseService = DatabaseService();

      // First, find employee by phone number
      final employee = await databaseService.getEmployeeByPhoneNumber(phoneNumber);

      if (employee == null) {
        debugPrint('❌ AUTH SERVICE: No employee found with phone: $phoneNumber');
        return AuthResult.failure('No account found with this phone number');
      }

      debugPrint('✅ AUTH SERVICE: Employee found: ${employee.name}, using email: ${employee.email}');

      // Sign in with Firebase Auth using the stored email
      final credential = await _firebaseService.auth.signInWithEmailAndPassword(
        email: employee.email,
        password: password,
      );

      if (credential.user != null) {
        await _updateUserDocument(credential.user!);
        await _firebaseService.logEvent('phone_sign_in_success');
        debugPrint('✅ AUTH SERVICE: Phone login successful for: ${employee.name}');
        return AuthResult.success(credential.user!);
      } else {
        return AuthResult.failure('Sign in failed');
      }
    } on FirebaseAuthException catch (e) {
      await _firebaseService.logError(e, StackTrace.current, reason: 'Phone sign in failed');
      debugPrint('❌ AUTH SERVICE: Phone login failed: ${e.code}');
      return AuthResult.failure(_getAuthErrorMessage(e.code));
    } catch (e) {
      await _firebaseService.logError(e, StackTrace.current, reason: 'Phone sign in failed');
      debugPrint('❌ AUTH SERVICE: Phone login failed: $e');
      return AuthResult.failure(e.toString());
    }
  }

  /// Create account with email and password
  Future<AuthResult> createAccountWithEmailAndPassword(String email, String password, String displayName) async {
    try {
      final credential = await _firebaseService.auth.createUserWithEmailAndPassword(
        email: email,
        password: password,
      );
      
      if (credential.user != null) {
        // Update display name
        await credential.user!.updateDisplayName(displayName);
        
        // Create user document
        await _createUserDocument(credential.user!, displayName: displayName);
        
        await _firebaseService.logEvent('account_creation_success');
        return AuthResult.success(credential.user!);
      } else {
        return AuthResult.failure('Account creation failed');
      }
    } on FirebaseAuthException catch (e) {
      await _firebaseService.logError(e, StackTrace.current, reason: 'Account creation failed');
      return AuthResult.failure(_getAuthErrorMessage(e.code));
    } catch (e) {
      await _firebaseService.logError(e, StackTrace.current, reason: 'Account creation failed');
      return AuthResult.failure(e.toString());
    }
  }

  /// Send password reset email
  Future<AuthResult> sendPasswordResetEmail(String email) async {
    try {
      await _firebaseService.auth.sendPasswordResetEmail(email: email);
      await _firebaseService.logEvent('password_reset_sent');
      return AuthResult.success(null, message: 'Password reset email sent');
    } on FirebaseAuthException catch (e) {
      await _firebaseService.logError(e, StackTrace.current, reason: 'Password reset failed');
      return AuthResult.failure(_getAuthErrorMessage(e.code));
    } catch (e) {
      await _firebaseService.logError(e, StackTrace.current, reason: 'Password reset failed');
      return AuthResult.failure(e.toString());
    }
  }

  /// Sign out
  Future<void> signOut() async {
    try {
      await _firebaseService.signOut();
      await _firebaseService.logEvent('sign_out_success');
    } catch (e) {
      await _firebaseService.logError(e, StackTrace.current, reason: 'Sign out failed');
    }
  }

  /// Delete account
  Future<AuthResult> deleteAccount() async {
    try {
      final user = currentUser;
      if (user != null) {
        // Delete user document from Firestore
        await _firebaseService.firestore.collection('users').doc(user.uid).delete();
        
        // Delete user account
        await user.delete();
        
        await _firebaseService.logEvent('account_deleted');
        return AuthResult.success(null, message: 'Account deleted successfully');
      } else {
        return AuthResult.failure('No user signed in');
      }
    } on FirebaseAuthException catch (e) {
      await _firebaseService.logError(e, StackTrace.current, reason: 'Account deletion failed');
      return AuthResult.failure(_getAuthErrorMessage(e.code));
    } catch (e) {
      await _firebaseService.logError(e, StackTrace.current, reason: 'Account deletion failed');
      return AuthResult.failure(e.toString());
    }
  }

  /// Create user document in Firestore
  Future<void> _createUserDocument(User user, {String? displayName}) async {
    try {
      final userDoc = _firebaseService.firestore.collection('users').doc(user.uid);
      
      await userDoc.set({
        'uid': user.uid,
        'email': user.email,
        'displayName': displayName ?? user.displayName ?? 'Anonymous User',
        'isAnonymous': user.isAnonymous,
        'createdAt': FieldValue.serverTimestamp(),
        'lastSignInAt': FieldValue.serverTimestamp(),
        'fcmToken': await _firebaseService.getFCMToken(),
        'appVersion': '1.0.0',
        'platform': Theme.of(NavigationService.navigatorKey.currentContext!).platform.name,
      });
    } catch (e) {
      debugPrint('Failed to create user document: $e');
    }
  }

  /// Update user document in Firestore
  Future<void> _updateUserDocument(User user) async {
    try {
      final userDoc = _firebaseService.firestore.collection('users').doc(user.uid);
      
      await userDoc.update({
        'lastSignInAt': FieldValue.serverTimestamp(),
        'fcmToken': await _firebaseService.getFCMToken(),
      });
    } catch (e) {
      debugPrint('Failed to update user document: $e');
    }
  }

  /// Get user-friendly error messages
  String _getAuthErrorMessage(String errorCode) {
    switch (errorCode) {
      case 'user-not-found':
        return 'No user found with this email address.';
      case 'wrong-password':
        return 'Incorrect password.';
      case 'email-already-in-use':
        return 'An account already exists with this email address.';
      case 'weak-password':
        return 'Password is too weak. Please choose a stronger password.';
      case 'invalid-email':
        return 'Invalid email address.';
      case 'user-disabled':
        return 'This account has been disabled.';
      case 'too-many-requests':
        return 'Too many failed attempts. Please try again later.';
      case 'requires-recent-login':
        return 'Please sign in again to complete this action.';
      default:
        return 'An error occurred. Please try again.';
    }
  }
}

/// Authentication result wrapper
class AuthResult {
  final bool isSuccess;
  final User? user;
  final String? error;
  final String? message;

  AuthResult.success(this.user, {this.message}) 
      : isSuccess = true, error = null;
  
  AuthResult.failure(this.error) 
      : isSuccess = false, user = null, message = null;
}

/// Navigation service for global context access
class NavigationService {
  static final GlobalKey<NavigatorState> navigatorKey = GlobalKey<NavigatorState>();
}
