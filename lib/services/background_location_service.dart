import 'dart:async';
import 'dart:isolate';
import 'dart:ui';
import 'package:flutter/foundation.dart';
import 'package:geolocator/geolocator.dart';
import 'package:shared_preferences/shared_preferences.dart';


class BackgroundLocationService {
  static const String _isolatePortName = 'location_tracking_isolate';
  static const String _isTrackingKey = 'is_background_tracking';
  static const String _employeeIdKey = 'tracking_employee_id';
  static const String _companyIdKey = 'tracking_company_id';
  
  static BackgroundLocationService? _instance;
  static BackgroundLocationService get instance {
    _instance ??= BackgroundLocationService._internal();
    return _instance!;
  }
  
  BackgroundLocationService._internal();
  
  ReceivePort? _receivePort;
  SendPort? _sendPort;
  bool _isInitialized = false;

  /// Initialize background location service
  Future<bool> initialize() async {
    if (_isInitialized) return true;
    
    try {
      // Create receive port for communication with isolate
      _receivePort = ReceivePort();
      
      // Register the port with a name for the isolate to find
      IsolateNameServer.registerPortWithName(
        _receivePort!.sendPort,
        _isolatePortName,
      );
      
      // Listen for messages from the isolate
      _receivePort!.listen(_handleIsolateMessage);
      
      _isInitialized = true;
      debugPrint('✅ Background location service initialized');
      return true;
    } catch (e) {
      debugPrint('❌ Failed to initialize background location service: $e');
      return false;
    }
  }

  /// Start background location tracking
  Future<bool> startBackgroundTracking(String employeeId, String companyId) async {
    try {
      if (!_isInitialized) {
        final initialized = await initialize();
        if (!initialized) return false;
      }

      // Store tracking info in SharedPreferences
      final prefs = await SharedPreferences.getInstance();
      await prefs.setBool(_isTrackingKey, true);
      await prefs.setString(_employeeIdKey, employeeId);
      await prefs.setString(_companyIdKey, companyId);

      // Start the background isolate
      await _startBackgroundIsolate();
      
      debugPrint('✅ Background location tracking started for employee: $employeeId');
      return true;
    } catch (e) {
      debugPrint('❌ Failed to start background tracking: $e');
      return false;
    }
  }

  /// Stop background location tracking
  Future<void> stopBackgroundTracking() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.setBool(_isTrackingKey, false);
      await prefs.remove(_employeeIdKey);
      await prefs.remove(_companyIdKey);

      // Send stop message to isolate
      _sendPort?.send({'action': 'stop'});
      
      debugPrint('🛑 Background location tracking stopped');
    } catch (e) {
      debugPrint('❌ Error stopping background tracking: $e');
    }
  }

  /// Check if background tracking is active
  Future<bool> isBackgroundTrackingActive() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      return prefs.getBool(_isTrackingKey) ?? false;
    } catch (e) {
      debugPrint('❌ Error checking background tracking status: $e');
      return false;
    }
  }

  /// Start the background isolate
  Future<void> _startBackgroundIsolate() async {
    try {
      await Isolate.spawn(
        _backgroundLocationIsolate,
        _receivePort!.sendPort,
        debugName: 'LocationTrackingIsolate',
      );
    } catch (e) {
      debugPrint('❌ Failed to start background isolate: $e');
    }
  }

  /// Handle messages from the background isolate
  void _handleIsolateMessage(dynamic message) {
    if (message is Map<String, dynamic>) {
      switch (message['type']) {
        case 'location_update':
          _handleLocationUpdate(message);
          break;
        case 'error':
          debugPrint('❌ Background isolate error: ${message['error']}');
          break;
        case 'status':
          debugPrint('📍 Background tracking status: ${message['message']}');
          break;
      }
    }
  }

  /// Handle location update from background isolate
  void _handleLocationUpdate(Map<String, dynamic> message) {
    debugPrint('📍 Background location update: ${message['latitude']}, ${message['longitude']}');
    // Location updates are handled by the isolate directly
    // This is just for logging/monitoring
  }

  /// Dispose resources
  void dispose() {
    _receivePort?.close();
    IsolateNameServer.removePortNameMapping(_isolatePortName);
    _isInitialized = false;
  }

  /// Background isolate entry point
  static void _backgroundLocationIsolate(SendPort mainSendPort) async {
    // Create a receive port for this isolate
    final isolateReceivePort = ReceivePort();
    
    // Send the isolate's send port to the main isolate
    mainSendPort.send(isolateReceivePort.sendPort);
    
    Timer? locationTimer;
    bool isTracking = false;
    
    try {
      // Listen for messages from main isolate
      isolateReceivePort.listen((message) async {
        if (message is Map<String, dynamic>) {
          switch (message['action']) {
            case 'start':
              if (!isTracking) {
                isTracking = true;
                locationTimer = Timer.periodic(
                  const Duration(minutes: 1), // Update every minute in background
                  (_) => _performBackgroundLocationUpdate(mainSendPort),
                );
                mainSendPort.send({
                  'type': 'status',
                  'message': 'Background tracking started'
                });
              }
              break;
            case 'stop':
              isTracking = false;
              locationTimer?.cancel();
              mainSendPort.send({
                'type': 'status',
                'message': 'Background tracking stopped'
              });
              break;
          }
        }
      });

      // Start initial tracking
      isTracking = true;
      locationTimer = Timer.periodic(
        const Duration(minutes: 1),
        (_) => _performBackgroundLocationUpdate(mainSendPort),
      );
      
    } catch (e) {
      mainSendPort.send({
        'type': 'error',
        'error': e.toString(),
      });
    }
  }

  /// Perform location update in background isolate
  static void _performBackgroundLocationUpdate(SendPort mainSendPort) async {
    try {
      // Check if tracking is still enabled
      final prefs = await SharedPreferences.getInstance();
      final isTracking = prefs.getBool(_isTrackingKey) ?? false;
      
      if (!isTracking) {
        return;
      }

      // Get current position
      final position = await Geolocator.getCurrentPosition(
        locationSettings: const LocationSettings(
          accuracy: LocationAccuracy.medium,
        ),
      );

      // Send location update to main isolate
      mainSendPort.send({
        'type': 'location_update',
        'latitude': position.latitude,
        'longitude': position.longitude,
        'timestamp': DateTime.now().toIso8601String(),
      });

      // TODO: Here you would typically:
      // 1. Check if employee is in office boundary
      // 2. Update database with current location
      // 3. Handle auto check-in/out if needed
      // Note: This requires Firebase initialization in isolate
      
    } catch (e) {
      mainSendPort.send({
        'type': 'error',
        'error': 'Background location update failed: $e',
      });
    }
  }
}

/// Background location callback for Android
@pragma('vm:entry-point')
void backgroundLocationCallback() {
  // This is called when the app is in background on Android
  // Handle location updates here
  debugPrint('📍 Background location callback triggered');
}
