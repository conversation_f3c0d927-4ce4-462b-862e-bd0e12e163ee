import 'package:flutter/material.dart';
import 'app_state_service.dart';

class NavigationService {
  static final NavigationService _instance = NavigationService._internal();
  factory NavigationService() => _instance;
  NavigationService._internal();

  static final GlobalKey<NavigatorState> navigatorKey = GlobalKey<NavigatorState>();
  
  BuildContext? get context => navigatorKey.currentContext;

  /// Navigate to appropriate screen based on user role
  Future<void> navigateToRoleBasedHome() async {
    final appState = AppStateService();
    
    switch (appState.currentRole) {
      case UserRole.employee:
        await navigateToEmployeeDashboard();
        break;
      case UserRole.companyOwner:
        await navigateToCompanyOwnerDashboard();
        break;
      case UserRole.superAdmin:
        await navigateToSuperAdminDashboard();
        break;
      case UserRole.none:
        await navigateToWelcomeScreen();
        break;
    }
  }

  /// Navigate to employee dashboard
  Future<void> navigateToEmployeeDashboard() async {
    await navigateAndClearStack('/employee-dashboard');
  }

  /// Navigate to company owner dashboard
  Future<void> navigateToCompanyOwnerDashboard({
    dynamic company,
    String accessCode = '',
  }) async {
    await navigateAndClearStack(
      '/company-owner-dashboard',
      arguments: {
        'company': company,
        'accessCode': accessCode,
      },
    );
  }

  /// Navigate to super admin dashboard
  Future<void> navigateToSuperAdminDashboard() async {
    await navigateAndClearStack('/super-admin-dashboard');
  }

  /// Navigate to welcome/onboarding screen
  Future<void> navigateToWelcomeScreen() async {
    await navigateAndClearStack('/welcome');
  }

  /// Navigate to employee onboarding
  Future<void> navigateToEmployeeOnboarding() async {
    await navigateTo('/employee-onboarding');
  }

  /// Navigate to login screen
  Future<void> navigateToLoginScreen() async {
    await navigateTo('/login');
  }

  /// Navigate to access code input screen
  Future<void> navigateToAccessCodeInput() async {
    debugPrint('🔍 NAVIGATION: Navigating to access code input screen...');
    await navigateTo('/access-code-input');
    debugPrint('✅ NAVIGATION: Navigation to access code input completed');
  }

  /// Navigate to company creation screen
  Future<void> navigateToCompanyCreation() async {
    await navigateTo('/company-creation');
  }

  /// Navigate to employee list screen
  Future<void> navigateToEmployeeList() async {
    await navigateTo('/employee-list');
  }

  /// Navigate to attendance reports screen
  Future<void> navigateToAttendanceReports() async {
    await navigateTo('/attendance-reports');
  }

  /// Navigate to employee settings screen
  Future<void> navigateToEmployeeSettings() async {
    await navigateTo('/employee-settings');
  }

  /// Navigate to attendance history screen
  Future<void> navigateToAttendanceHistory() async {
    await navigateTo('/attendance-history');
  }

  /// Navigate to location history screen
  Future<void> navigateToLocationHistory() async {
    await navigateTo('/location-history');
  }

  /// Generic navigation method
  Future<void> navigateTo(String routeName, {Object? arguments}) async {
    debugPrint('🔍 NAVIGATION: Attempting to navigate to: $routeName');
    if (context != null) {
      debugPrint('✅ NAVIGATION: Context available, pushing route: $routeName');
      await Navigator.of(context!).pushNamed(routeName, arguments: arguments);
      debugPrint('✅ NAVIGATION: Successfully navigated to: $routeName');
    } else {
      debugPrint('❌ NAVIGATION: Context is null! Cannot navigate to: $routeName');
    }
  }

  /// Navigate and replace current route
  Future<void> navigateAndReplace(String routeName, {Object? arguments}) async {
    if (context != null) {
      await Navigator.of(context!).pushReplacementNamed(routeName, arguments: arguments);
    }
  }

  /// Navigate and clear entire stack
  Future<void> navigateAndClearStack(String routeName, {Object? arguments}) async {
    if (context != null) {
      await Navigator.of(context!).pushNamedAndRemoveUntil(
        routeName,
        (route) => false,
        arguments: arguments,
      );
    }
  }

  /// Go back to previous screen
  void goBack({Object? result}) {
    if (context != null && Navigator.of(context!).canPop()) {
      Navigator.of(context!).pop(result);
    }
  }

  /// Show modal bottom sheet
  Future<T?> showBottomSheet<T>({
    required Widget child,
    bool isScrollControlled = false,
    bool isDismissible = true,
    bool enableDrag = true,
  }) async {
    if (context != null) {
      return await showModalBottomSheet<T>(
        context: context!,
        isScrollControlled: isScrollControlled,
        isDismissible: isDismissible,
        enableDrag: enableDrag,
        builder: (context) => child,
      );
    }
    return null;
  }

  /// Show dialog
  Future<T?> showCustomDialog<T>({
    required Widget child,
    bool barrierDismissible = true,
  }) async {
    if (context != null) {
      return await showDialog<T>(
        context: context!,
        barrierDismissible: barrierDismissible,
        builder: (context) => child,
      );
    }
    return null;
  }

  /// Show snackbar
  void showSnackBar({
    required String message,
    Color? backgroundColor,
    Duration duration = const Duration(seconds: 3),
    SnackBarAction? action,
  }) {
    if (context != null) {
      ScaffoldMessenger.of(context!).showSnackBar(
        SnackBar(
          content: Text(message),
          backgroundColor: backgroundColor,
          duration: duration,
          action: action,
        ),
      );
    }
  }

  /// Show success message
  void showSuccess(String message) {
    showSnackBar(
      message: message,
      backgroundColor: Colors.green,
    );
  }

  /// Show error message
  void showError(String message) {
    showSnackBar(
      message: message,
      backgroundColor: Colors.red,
    );
  }

  /// Show info message
  void showInfo(String message) {
    showSnackBar(
      message: message,
      backgroundColor: Colors.blue,
    );
  }

  /// Show warning message
  void showWarning(String message) {
    showSnackBar(
      message: message,
      backgroundColor: Colors.orange,
    );
  }
}
