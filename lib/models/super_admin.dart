import 'package:cloud_firestore/cloud_firestore.dart';

class SuperAdmin {
  final String id;
  final String accessCode;
  final DateTime createdAt;
  final DateTime updatedAt;
  final bool isActive;

  SuperAdmin({
    required this.id,
    required this.accessCode,
    required this.createdAt,
    required this.updatedAt,
    this.isActive = true,
  });

  // Convert SuperAdmin to Map for Firestore
  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'accessCode': accessCode,
      'createdAt': Timestamp.fromDate(createdAt),
      'updatedAt': Timestamp.fromDate(updatedAt),
      'isActive': isActive,
    };
  }

  // Create SuperAdmin from Firestore document
  factory SuperAdmin.fromMap(Map<String, dynamic> map, String documentId) {
    return SuperAdmin(
      id: documentId,
      accessCode: map['accessCode'] ?? '',
      createdAt: (map['createdAt'] as Timestamp?)?.toDate() ?? DateTime.now(),
      updatedAt: (map['updatedAt'] as Timestamp?)?.toDate() ?? DateTime.now(),
      isActive: map['isActive'] ?? true,
    );
  }

  // Create SuperAdmin from Firestore DocumentSnapshot
  factory SuperAdmin.fromSnapshot(DocumentSnapshot snapshot) {
    final data = snapshot.data() as Map<String, dynamic>;
    return SuperAdmin.fromMap(data, snapshot.id);
  }

  // Copy with method for updates
  SuperAdmin copyWith({
    String? id,
    String? accessCode,
    DateTime? createdAt,
    DateTime? updatedAt,
    bool? isActive,
  }) {
    return SuperAdmin(
      id: id ?? this.id,
      accessCode: accessCode ?? this.accessCode,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
      isActive: isActive ?? this.isActive,
    );
  }

  @override
  String toString() {
    return 'SuperAdmin(id: $id, accessCode: $accessCode, isActive: $isActive)';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is SuperAdmin && other.id == id;
  }

  @override
  int get hashCode => id.hashCode;
}
