import 'package:cloud_firestore/cloud_firestore.dart';

class Attendance {
  final String id;
  final String employeeId;
  final String employeeName;
  final String companyId;
  final DateTime date;
  final DateTime checkInTime;
  final double checkInLatitude;
  final double checkInLongitude;
  final DateTime? checkOutTime;
  final double? checkOutLatitude;
  final double? checkOutLongitude;
  final String? notes;
  final DateTime createdAt;
  final DateTime updatedAt;

  Attendance({
    required this.id,
    required this.employeeId,
    required this.employeeName,
    required this.companyId,
    required this.date,
    required this.checkInTime,
    required this.checkInLatitude,
    required this.checkInLongitude,
    this.checkOutTime,
    this.checkOutLatitude,
    this.checkOutLongitude,
    this.notes,
    required this.createdAt,
    required this.updatedAt,
  });

  // Convert Attendance to Map for Firestore
  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'employeeId': employeeId,
      'employeeName': employeeName,
      'companyId': companyId,
      'date': Timestamp.fromDate(date),
      'checkInTime': Timestamp.fromDate(checkInTime),
      'checkInLatitude': checkInLatitude,
      'checkInLongitude': checkInLongitude,
      'checkOutTime': checkOutTime != null ? Timestamp.fromDate(checkOutTime!) : null,
      'checkOutLatitude': checkOutLatitude,
      'checkOutLongitude': checkOutLongitude,
      'notes': notes,
      'createdAt': Timestamp.fromDate(createdAt),
      'updatedAt': Timestamp.fromDate(updatedAt),
    };
  }

  // Create Attendance from Firestore document
  factory Attendance.fromMap(Map<String, dynamic> map, String documentId) {
    return Attendance(
      id: documentId,
      employeeId: map['employeeId'] ?? '',
      employeeName: map['employeeName'] ?? '',
      companyId: map['companyId'] ?? '',
      date: (map['date'] as Timestamp?)?.toDate() ?? DateTime.now(),
      checkInTime: (map['checkInTime'] as Timestamp?)?.toDate() ?? DateTime.now(),
      checkInLatitude: (map['checkInLatitude'] as num?)?.toDouble() ?? 0.0,
      checkInLongitude: (map['checkInLongitude'] as num?)?.toDouble() ?? 0.0,
      checkOutTime: (map['checkOutTime'] as Timestamp?)?.toDate(),
      checkOutLatitude: (map['checkOutLatitude'] as num?)?.toDouble(),
      checkOutLongitude: (map['checkOutLongitude'] as num?)?.toDouble(),
      notes: map['notes'],
      createdAt: (map['createdAt'] as Timestamp?)?.toDate() ?? DateTime.now(),
      updatedAt: (map['updatedAt'] as Timestamp?)?.toDate() ?? DateTime.now(),
    );
  }

  // Create Attendance from Firestore DocumentSnapshot
  factory Attendance.fromSnapshot(DocumentSnapshot snapshot) {
    final data = snapshot.data() as Map<String, dynamic>;
    return Attendance.fromMap(data, snapshot.id);
  }

  // Copy with method for updates
  Attendance copyWith({
    String? id,
    String? employeeId,
    String? employeeName,
    String? companyId,
    DateTime? date,
    DateTime? checkInTime,
    double? checkInLatitude,
    double? checkInLongitude,
    DateTime? checkOutTime,
    double? checkOutLatitude,
    double? checkOutLongitude,
    String? notes,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) {
    return Attendance(
      id: id ?? this.id,
      employeeId: employeeId ?? this.employeeId,
      employeeName: employeeName ?? this.employeeName,
      companyId: companyId ?? this.companyId,
      date: date ?? this.date,
      checkInTime: checkInTime ?? this.checkInTime,
      checkInLatitude: checkInLatitude ?? this.checkInLatitude,
      checkInLongitude: checkInLongitude ?? this.checkInLongitude,
      checkOutTime: checkOutTime ?? this.checkOutTime,
      checkOutLatitude: checkOutLatitude ?? this.checkOutLatitude,
      checkOutLongitude: checkOutLongitude ?? this.checkOutLongitude,
      notes: notes ?? this.notes,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
    );
  }

  // Calculate total work hours
  Duration? get workDuration {
    if (checkOutTime != null) {
      return checkOutTime!.difference(checkInTime);
    }
    return null;
  }

  // Check if employee is currently checked in
  bool get isCheckedIn => checkOutTime == null;

  // Format work duration as string
  String get workDurationString {
    final duration = workDuration;
    if (duration == null) return 'Still working...';
    
    final hours = duration.inHours;
    final minutes = duration.inMinutes.remainder(60);
    return '${hours}h ${minutes}m';
  }

  @override
  String toString() {
    return 'Attendance(id: $id, employeeId: $employeeId, date: $date, checkIn: $checkInTime, checkOut: $checkOutTime)';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is Attendance && other.id == id;
  }

  @override
  int get hashCode => id.hashCode;
}
