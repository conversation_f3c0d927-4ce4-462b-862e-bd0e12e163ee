import 'package:cloud_firestore/cloud_firestore.dart';

enum AttendanceStatus {
  checkedIn,
  checkedOut,
  inOffice,
  outOfOffice,
}

class AttendanceRecord {
  final String id;
  final String employeeId;
  final String companyId;
  final double latitude;
  final double longitude;
  final String locationAddress;
  final AttendanceStatus status;
  final DateTime timestamp;
  final DateTime date; // Date only (for daily grouping)
  final bool isWithinRadius;
  final double distanceFromOffice; // in meters
  final bool isSynced; // for offline support

  AttendanceRecord({
    required this.id,
    required this.employeeId,
    required this.companyId,
    required this.latitude,
    required this.longitude,
    required this.locationAddress,
    required this.status,
    required this.timestamp,
    required this.date,
    required this.isWithinRadius,
    required this.distanceFromOffice,
    this.isSynced = false,
  });

  // Convert AttendanceRecord to Map for Firestore
  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'employeeId': employeeId,
      'companyId': companyId,
      'latitude': latitude,
      'longitude': longitude,
      'locationAddress': locationAddress,
      'status': status.name,
      'timestamp': Timestamp.fromDate(timestamp),
      'date': Timestamp.fromDate(date),
      'isWithinRadius': isWithinRadius,
      'distanceFromOffice': distanceFromOffice,
      'isSynced': isSynced,
    };
  }

  // Create AttendanceRecord from Firestore document
  factory AttendanceRecord.fromMap(Map<String, dynamic> map, String documentId) {
    return AttendanceRecord(
      id: documentId,
      employeeId: map['employeeId'] ?? '',
      companyId: map['companyId'] ?? '',
      latitude: (map['latitude'] ?? 0.0).toDouble(),
      longitude: (map['longitude'] ?? 0.0).toDouble(),
      locationAddress: map['locationAddress'] ?? '',
      status: AttendanceStatus.values.firstWhere(
        (e) => e.name == map['status'],
        orElse: () => AttendanceStatus.outOfOffice,
      ),
      timestamp: (map['timestamp'] as Timestamp?)?.toDate() ?? DateTime.now(),
      date: (map['date'] as Timestamp?)?.toDate() ?? DateTime.now(),
      isWithinRadius: map['isWithinRadius'] ?? false,
      distanceFromOffice: (map['distanceFromOffice'] ?? 0.0).toDouble(),
      isSynced: map['isSynced'] ?? false,
    );
  }

  // Create AttendanceRecord from Firestore DocumentSnapshot
  factory AttendanceRecord.fromSnapshot(DocumentSnapshot snapshot) {
    final data = snapshot.data() as Map<String, dynamic>;
    return AttendanceRecord.fromMap(data, snapshot.id);
  }

  // Copy with method for updates
  AttendanceRecord copyWith({
    String? id,
    String? employeeId,
    String? companyId,
    double? latitude,
    double? longitude,
    String? locationAddress,
    AttendanceStatus? status,
    DateTime? timestamp,
    DateTime? date,
    bool? isWithinRadius,
    double? distanceFromOffice,
    bool? isSynced,
  }) {
    return AttendanceRecord(
      id: id ?? this.id,
      employeeId: employeeId ?? this.employeeId,
      companyId: companyId ?? this.companyId,
      latitude: latitude ?? this.latitude,
      longitude: longitude ?? this.longitude,
      locationAddress: locationAddress ?? this.locationAddress,
      status: status ?? this.status,
      timestamp: timestamp ?? this.timestamp,
      date: date ?? this.date,
      isWithinRadius: isWithinRadius ?? this.isWithinRadius,
      distanceFromOffice: distanceFromOffice ?? this.distanceFromOffice,
      isSynced: isSynced ?? this.isSynced,
    );
  }

  @override
  String toString() {
    return 'AttendanceRecord(id: $id, employeeId: $employeeId, status: $status, timestamp: $timestamp, isWithinRadius: $isWithinRadius)';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is AttendanceRecord && other.id == id;
  }

  @override
  int get hashCode => id.hashCode;
}
