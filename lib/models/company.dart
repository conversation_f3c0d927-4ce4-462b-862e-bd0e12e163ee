import 'package:cloud_firestore/cloud_firestore.dart';

class Company {
  final String id;
  final String name;
  final String pincode;
  final double latitude;
  final double longitude;
  final String locationAddress;
  final String ownerCode;
  final String companyCode; // Code for employees to use during onboarding
  final DateTime createdAt;
  final DateTime updatedAt;

  Company({
    required this.id,
    required this.name,
    required this.pincode,
    required this.latitude,
    required this.longitude,
    required this.locationAddress,
    required this.ownerCode,
    required this.companyCode,
    required this.createdAt,
    required this.updatedAt,
  });

  // Convert Company to Map for Firestore
  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'name': name,
      'pincode': pincode,
      'latitude': latitude,
      'longitude': longitude,
      'locationAddress': locationAddress,
      'ownerCode': ownerCode,
      'companyCode': companyCode,
      'createdAt': Timestamp.fromDate(createdAt),
      'updatedAt': Timestamp.fromDate(updatedAt),
    };
  }

  // Create Company from Firestore document
  factory Company.fromMap(Map<String, dynamic> map, String documentId) {
    return Company(
      id: documentId,
      name: map['name'] ?? '',
      pincode: map['pincode'] ?? '',
      latitude: (map['latitude'] ?? 0.0).toDouble(),
      longitude: (map['longitude'] ?? 0.0).toDouble(),
      locationAddress: map['locationAddress'] ?? '',
      ownerCode: map['ownerCode'] ?? '',
      companyCode: map['companyCode'] ?? '',
      createdAt: (map['createdAt'] as Timestamp?)?.toDate() ?? DateTime.now(),
      updatedAt: (map['updatedAt'] as Timestamp?)?.toDate() ?? DateTime.now(),
    );
  }

  // Create Company from Firestore DocumentSnapshot
  factory Company.fromSnapshot(DocumentSnapshot snapshot) {
    final data = snapshot.data() as Map<String, dynamic>;
    return Company.fromMap(data, snapshot.id);
  }

  // Copy with method for updates
  Company copyWith({
    String? id,
    String? name,
    String? pincode,
    double? latitude,
    double? longitude,
    String? locationAddress,
    String? ownerCode,
    String? companyCode,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) {
    return Company(
      id: id ?? this.id,
      name: name ?? this.name,
      pincode: pincode ?? this.pincode,
      latitude: latitude ?? this.latitude,
      longitude: longitude ?? this.longitude,
      locationAddress: locationAddress ?? this.locationAddress,
      ownerCode: ownerCode ?? this.ownerCode,
      companyCode: companyCode ?? this.companyCode,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
    );
  }

  @override
  String toString() {
    return 'Company(id: $id, name: $name, pincode: $pincode, ownerCode: $ownerCode, companyCode: $companyCode)';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is Company && other.id == id;
  }

  @override
  int get hashCode => id.hashCode;
}
