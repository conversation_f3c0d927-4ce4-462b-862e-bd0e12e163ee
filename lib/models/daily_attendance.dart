import 'package:cloud_firestore/cloud_firestore.dart';

class DailyAttendance {
  final String id;
  final String employeeId;
  final String companyId;
  final DateTime date;
  final DateTime? firstCheckIn;
  final DateTime? lastCheckOut;
  final int totalTimeInOffice; // in minutes
  final int numberOfEntries;
  final int numberOfExits;
  final bool isPresent;
  final DateTime createdAt;
  final DateTime updatedAt;

  DailyAttendance({
    required this.id,
    required this.employeeId,
    required this.companyId,
    required this.date,
    this.firstCheckIn,
    this.lastCheckOut,
    this.totalTimeInOffice = 0,
    this.numberOfEntries = 0,
    this.numberOfExits = 0,
    this.isPresent = false,
    required this.createdAt,
    required this.updatedAt,
  });

  // Convert DailyAttendance to Map for Firestore
  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'employeeId': employeeId,
      'companyId': companyId,
      'date': Timestamp.fromDate(date),
      'firstCheckIn': firstCheckIn != null ? Timestamp.fromDate(firstCheckIn!) : null,
      'lastCheckOut': lastCheckOut != null ? Timestamp.fromDate(lastCheckOut!) : null,
      'totalTimeInOffice': totalTimeInOffice,
      'numberOfEntries': numberOfEntries,
      'numberOfExits': numberOfExits,
      'isPresent': isPresent,
      'createdAt': Timestamp.fromDate(createdAt),
      'updatedAt': Timestamp.fromDate(updatedAt),
    };
  }

  // Create DailyAttendance from Firestore document
  factory DailyAttendance.fromMap(Map<String, dynamic> map, String documentId) {
    return DailyAttendance(
      id: documentId,
      employeeId: map['employeeId'] ?? '',
      companyId: map['companyId'] ?? '',
      date: (map['date'] as Timestamp?)?.toDate() ?? DateTime.now(),
      firstCheckIn: (map['firstCheckIn'] as Timestamp?)?.toDate(),
      lastCheckOut: (map['lastCheckOut'] as Timestamp?)?.toDate(),
      totalTimeInOffice: map['totalTimeInOffice'] ?? 0,
      numberOfEntries: map['numberOfEntries'] ?? 0,
      numberOfExits: map['numberOfExits'] ?? 0,
      isPresent: map['isPresent'] ?? false,
      createdAt: (map['createdAt'] as Timestamp?)?.toDate() ?? DateTime.now(),
      updatedAt: (map['updatedAt'] as Timestamp?)?.toDate() ?? DateTime.now(),
    );
  }

  // Create DailyAttendance from Firestore DocumentSnapshot
  factory DailyAttendance.fromSnapshot(DocumentSnapshot snapshot) {
    final data = snapshot.data() as Map<String, dynamic>;
    return DailyAttendance.fromMap(data, snapshot.id);
  }

  // Copy with method for updates
  DailyAttendance copyWith({
    String? id,
    String? employeeId,
    String? companyId,
    DateTime? date,
    DateTime? firstCheckIn,
    DateTime? lastCheckOut,
    int? totalTimeInOffice,
    int? numberOfEntries,
    int? numberOfExits,
    bool? isPresent,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) {
    return DailyAttendance(
      id: id ?? this.id,
      employeeId: employeeId ?? this.employeeId,
      companyId: companyId ?? this.companyId,
      date: date ?? this.date,
      firstCheckIn: firstCheckIn ?? this.firstCheckIn,
      lastCheckOut: lastCheckOut ?? this.lastCheckOut,
      totalTimeInOffice: totalTimeInOffice ?? this.totalTimeInOffice,
      numberOfEntries: numberOfEntries ?? this.numberOfEntries,
      numberOfExits: numberOfExits ?? this.numberOfExits,
      isPresent: isPresent ?? this.isPresent,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
    );
  }

  // Helper methods
  String get formattedTotalTime {
    final hours = totalTimeInOffice ~/ 60;
    final minutes = totalTimeInOffice % 60;
    return '${hours}h ${minutes}m';
  }

  bool get isCurrentlyInOffice {
    return numberOfEntries > numberOfExits;
  }

  @override
  String toString() {
    return 'DailyAttendance(id: $id, employeeId: $employeeId, date: $date, totalTime: $formattedTotalTime, isPresent: $isPresent)';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is DailyAttendance && other.id == id;
  }

  @override
  int get hashCode => id.hashCode;
}
