import 'package:cloud_firestore/cloud_firestore.dart';

class Employee {
  final String id;
  final String name;
  final String phoneNumber; // Primary login field
  final String employeeId;
  final String designation;
  final String email; // Optional field
  final String companyId;
  final String companyName;
  final DateTime createdAt;
  final DateTime updatedAt;
  final bool isActive;

  Employee({
    required this.id,
    required this.name,
    required this.phoneNumber, // Now required for login
    this.employeeId = '',
    required this.designation,
    this.email = '', // Now optional
    required this.companyId,
    required this.companyName,
    required this.createdAt,
    required this.updatedAt,
    this.isActive = true,
  });

  // Convert Employee to Map for Firestore
  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'name': name,
      'email': email,
      'employeeId': employeeId,
      'designation': designation,
      'phoneNumber': phoneNumber,
      'companyId': companyId,
      'companyName': companyName,
      'createdAt': Timestamp.fromDate(createdAt),
      'updatedAt': Timestamp.fromDate(updatedAt),
      'isActive': isActive,
    };
  }

  // Create Employee from Firestore document
  factory Employee.fromMap(Map<String, dynamic> map, String documentId) {
    return Employee(
      id: documentId,
      name: map['name'] ?? '',
      email: map['email'] ?? '',
      employeeId: map['employeeId'] ?? '',
      designation: map['designation'] ?? '',
      phoneNumber: map['phoneNumber'] ?? '',
      companyId: map['companyId'] ?? '',
      companyName: map['companyName'] ?? '',
      createdAt: (map['createdAt'] as Timestamp?)?.toDate() ?? DateTime.now(),
      updatedAt: (map['updatedAt'] as Timestamp?)?.toDate() ?? DateTime.now(),
      isActive: map['isActive'] ?? true,
    );
  }

  // Create Employee from Firestore DocumentSnapshot
  factory Employee.fromSnapshot(DocumentSnapshot snapshot) {
    final data = snapshot.data() as Map<String, dynamic>;
    return Employee.fromMap(data, snapshot.id);
  }

  // Copy with method for updates
  Employee copyWith({
    String? id,
    String? name,
    String? phoneNumber,
    String? employeeId,
    String? designation,
    String? email,
    String? companyId,
    String? companyName,
    DateTime? createdAt,
    DateTime? updatedAt,
    bool? isActive,
  }) {
    return Employee(
      id: id ?? this.id,
      name: name ?? this.name,
      phoneNumber: phoneNumber ?? this.phoneNumber,
      employeeId: employeeId ?? this.employeeId,
      designation: designation ?? this.designation,
      email: email ?? this.email,
      companyId: companyId ?? this.companyId,
      companyName: companyName ?? this.companyName,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
      isActive: isActive ?? this.isActive,
    );
  }

  @override
  String toString() {
    return 'Employee(id: $id, name: $name, phoneNumber: $phoneNumber, employeeId: $employeeId, designation: $designation, companyId: $companyId)';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is Employee && other.id == id;
  }

  @override
  int get hashCode => id.hashCode;
}
