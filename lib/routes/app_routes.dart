import 'package:flutter/material.dart';
import '../screens/splash_screen.dart';
import '../screens/auth/login_screen.dart';
import '../screens/welcome_screen.dart';
import '../screens/employee/employee_onboarding_screen.dart';
import '../screens/employee/employee_dashboard_screen.dart';
import '../screens/employee/employee_settings_screen.dart';
import '../screens/employee/location_history_screen.dart';
import '../screens/company_owner/company_owner_dashboard_screen.dart';
import '../screens/company_owner/employee_list_screen.dart';
import '../screens/company_owner/attendance_reports_screen.dart';
import '../screens/super_admin/super_admin_dashboard_screen.dart';
import '../screens/super_admin/company_creation_screen.dart';
import '../screens/shared/access_code_input_screen.dart';

class AppRoutes {
  // Route names
  static const String splash = '/';
  static const String welcome = '/welcome';
  static const String login = '/login';
  static const String accessCodeInput = '/access-code-input';

  // Employee routes
  static const String employeeOnboarding = '/employee-onboarding';
  static const String employeeDashboard = '/employee-dashboard';
  static const String employeeSettings = '/employee-settings';
  static const String locationHistory = '/location-history';
  
  // Company Owner routes
  static const String companyOwnerDashboard = '/company-owner-dashboard';
  static const String employeeList = '/employee-list';
  static const String attendanceReports = '/attendance-reports';
  
  // Super Admin routes
  static const String superAdminDashboard = '/super-admin-dashboard';
  static const String companyCreation = '/company-creation';

  /// Generate routes
  static Route<dynamic> generateRoute(RouteSettings settings) {
    switch (settings.name) {
      case splash:
        return MaterialPageRoute(
          builder: (_) => const SplashScreen(),
          settings: settings,
        );
        
      case welcome:
        return MaterialPageRoute(
          builder: (_) => const WelcomeScreen(),
          settings: settings,
        );

      case login:
        return MaterialPageRoute(
          builder: (_) => const LoginScreen(),
          settings: settings,
        );

      case accessCodeInput:
        return MaterialPageRoute(
          builder: (_) => const AccessCodeInputScreen(),
          settings: settings,
        );
        
      // Employee routes
      case employeeOnboarding:
        return MaterialPageRoute(
          builder: (_) => const EmployeeOnboardingScreen(),
          settings: settings,
        );
        
      case employeeDashboard:
        return MaterialPageRoute(
          builder: (_) => const EmployeeDashboardScreen(),
          settings: settings,
        );
        
      case employeeSettings:
        return MaterialPageRoute(
          builder: (_) => const EmployeeSettingsScreen(),
          settings: settings,
        );
        
      case locationHistory:
        return MaterialPageRoute(
          builder: (_) => const LocationHistoryScreen(),
          settings: settings,
        );
        
      // Company Owner routes
      case companyOwnerDashboard:
        final args = settings.arguments as Map<String, dynamic>?;
        return MaterialPageRoute(
          builder: (_) => CompanyOwnerDashboardScreen(
            company: args?['company'],
            accessCode: args?['accessCode'] ?? '',
          ),
          settings: settings,
        );
        
      case employeeList:
        return MaterialPageRoute(
          builder: (_) => const EmployeeListScreen(),
          settings: settings,
        );
        
      case attendanceReports:
        return MaterialPageRoute(
          builder: (_) => const AttendanceReportsScreen(),
          settings: settings,
        );
        
      // Super Admin routes
      case superAdminDashboard:
        return MaterialPageRoute(
          builder: (_) => const SuperAdminDashboardScreen(),
          settings: settings,
        );
        
      case companyCreation:
        return MaterialPageRoute(
          builder: (_) => const CompanyCreationScreen(),
          settings: settings,
        );
        
      default:
        return MaterialPageRoute(
          builder: (_) => const NotFoundScreen(),
          settings: settings,
        );
    }
  }

  /// Get all route names
  static List<String> get allRoutes => [
    splash,
    welcome,
    accessCodeInput,
    employeeOnboarding,
    employeeDashboard,
    employeeSettings,
    locationHistory,
    companyOwnerDashboard,
    employeeList,
    attendanceReports,
    superAdminDashboard,
    companyCreation,
  ];
}

/// 404 Not Found Screen
class NotFoundScreen extends StatelessWidget {
  const NotFoundScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Page Not Found'),
      ),
      body: const Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.error_outline,
              size: 64,
              color: Colors.grey,
            ),
            SizedBox(height: 16),
            Text(
              '404 - Page Not Found',
              style: TextStyle(
                fontSize: 24,
                fontWeight: FontWeight.bold,
                color: Colors.grey,
              ),
            ),
            SizedBox(height: 8),
            Text(
              'The page you are looking for does not exist.',
              style: TextStyle(
                fontSize: 16,
                color: Colors.grey,
              ),
            ),
          ],
        ),
      ),
    );
  }
}
