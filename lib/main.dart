import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'services/firebase_service.dart';
import 'services/super_admin_service.dart';
import 'services/navigation_service.dart';
import 'services/network_service.dart';
import 'routes/app_routes.dart';

void main() async {
  WidgetsFlutterBinding.ensureInitialized();

  // Set preferred orientations
  await SystemChrome.setPreferredOrientations([
    DeviceOrientation.portraitUp,
    DeviceOrientation.portraitDown,
  ]);

  // Initialize Firebase in background (non-blocking)
  _initializeServices();

  runApp(const TimeSyncApp());
}

// Initialize services in background
void _initializeServices() async {
  try {
    await FirebaseService().initialize();
    await NetworkService().initialize();
    await SuperAdminService().initializeSuperAdmin();
    debugPrint('✅ All services initialized successfully');
  } catch (e) {
    debugPrint('❌ Service initialization failed: $e');
  }
}

class TimeSyncApp extends StatelessWidget {
  const TimeSyncApp({super.key});

  @override
  Widget build(BuildContext context) {
    return MaterialApp(
      title: 'timeSync',
      debugShowCheckedModeBanner: false,
      navigatorKey: NavigationService.navigatorKey,
      theme: ThemeData(
        colorScheme: ColorScheme.fromSeed(
          seedColor: const Color(0xFF667eea),
          brightness: Brightness.light,
        ),
        useMaterial3: true,
        appBarTheme: const AppBarTheme(
          backgroundColor: Color(0xFF667eea),
          foregroundColor: Colors.white,
          elevation: 0,
        ),
      ),
      onGenerateRoute: AppRoutes.generateRoute,
      initialRoute: AppRoutes.splash,
    );
  }
}


