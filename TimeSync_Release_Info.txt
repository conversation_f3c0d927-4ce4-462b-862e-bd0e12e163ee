📱 TimeSync Attendance App - Release APK
==========================================

🚀 BUILD INFORMATION
--------------------
App Name: TimeSync
Version: 1.0
Build Type: Release
File Name: TimeSync_v1.0_Release.apk
File Size: 50MB
Build Date: July 20, 2025

🔧 TECHNICAL DETAILS
--------------------
Platform: Android
Framework: Flutter
State Management: Riverpod
Database: Firebase Firestore
Authentication: Firebase Auth
Location Services: GPS/Location tracking
Minimum Android Version: API 21 (Android 5.0)

✨ KEY FEATURES
---------------
• Three User Roles:
  - Super Admin (Secret code access)
  - Company Owner (Company code login)
  - Employee (Registration with company selection)

• Employee Management:
  - Employee registration and onboarding
  - View employee lists with search functionality
  - Employee details and profiles

• Attendance Tracking:
  - Real-time location tracking
  - Automatic attendance detection
  - Attendance reports and analytics
  - Present/Absent status tracking

• Company Management:
  - Multiple company support
  - Company-specific access codes
  - Company dashboard with quick actions

• Authentication:
  - Secure login system
  - Automatic login for authenticated users
  - Role-based access control

🏢 SAMPLE COMPANIES (For Testing)
---------------------------------
1. Tata Consultancy Services
   - Owner Code: TCS2024OWNER
   - Company Code: TCS2024

2. Apple Inc.
   - Owner Code: APPLE2024OWNER
   - Company Code: APPLE2024

3. Google LLC
   - Owner Code: GOOGLE2024OWNER
   - Company Code: GOOGLE2024

4. Microsoft Corporation
   - Owner Code: MSFT2024OWNER
   - Company Code: MSFT2024

5. Meta Platforms Inc.
   - Owner Code: META2024OWNER
   - Company Code: META2024

6. Samsung Electronics
   - Owner Code: SAMSUNG24OWNER
   - Company Code: SAMSUNG24

🔐 SUPER ADMIN ACCESS
--------------------
Secret Code: SUPER_ADMIN_2024

📱 INSTALLATION INSTRUCTIONS
----------------------------
1. Enable "Unknown Sources" in Android Settings:
   Settings > Security > Unknown Sources (Enable)

2. Transfer the APK file to your Android device

3. Tap on the APK file to install

4. Grant necessary permissions when prompted:
   - Location access (for attendance tracking)
   - Storage access (for app data)

⚠️ IMPORTANT NOTES
------------------
• This is a release build optimized for production use
• All sample data is included for testing purposes
• Location services must be enabled for proper functionality
• Internet connection required for Firebase sync
• App works in offline mode when network is unavailable

🧪 TESTING GUIDE
----------------
1. Test Super Admin:
   - Use secret code: SUPER_ADMIN_2024
   - Create new companies and manage system

2. Test Company Owner:
   - Use any company code (e.g., TCS2024)
   - View employees and attendance reports

3. Test Employee:
   - Register with name, designation, and company
   - Test location tracking and attendance

📞 SUPPORT
----------
For technical support or issues, please contact the development team.

Built with ❤️ using Flutter & Firebase
